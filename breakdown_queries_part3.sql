-- Breakdown of f_search_auction_items function for pgAdmin4 execution - Part 3
-- Final queries for bid status and items

-- Set tenant context (if using RLS)
SET app.current_tenant = '1';

-- Query 11: Count lot items
WITH count_lot_items AS (
  SELECT TLD.lot_no, count(*) as count
    FROM t_lot_detail TLD
    JOIN t_exhibition_item TEI ON TEI.lot_no = TLD.lot_no
    JOIN (
      SELECT TE.exhibition_no
        FROM t_exhibition TE
        JOIN (
            SELECT TEI.exhibition_no,
                    MAX(CASE
                          WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                          ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                      END) as extended_end_datetime
              FROM t_exhibition_item TEI
            WHERE TEI.delete_flag=0
              AND TEI.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
            GROUP BY TEI.exhibition_no
        ) TEI_MAX ON TEI_MAX.exhibition_no = TE.exhibition_no
        LEFT JOIN t_exhibition_member TEM
          ON TEM.exhibition_no = TE.exhibition_no
          AND TEM.member_no = 10
        WHERE TE.tenant_no = 1
        AND TE.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
          AND TE.exhibition_classification_info->>'auctionClassification' = '2'
          AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
          AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
          AND now() < TEI_MAX.extended_end_datetime
          AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
    ) FE ON FE.exhibition_no = TEI.exhibition_no
    WHERE TLD.tenant_no = 1
    GROUP BY TLD.lot_no
)
SELECT * FROM count_lot_items LIMIT 100;

-- Query 12: Get bid status (this calls another function)
-- Note: This query calls f_get_auction_item_bid_status function
-- You may need to run the filter_item query first to get exhibition_item_nos
WITH filter_exhibition AS (
  SELECT TE.exhibition_no,
          TE.category_id,
          TE.pitch_width,
          TE.more_little_judge_pitch,
          TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
          TE.start_datetime,
          TE.end_datetime,
          TE.preview_end_datetime,
          TEI_MAX.extended_end_datetime,
          TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag=0
          AND TEI.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
        GROUP BY TEI.exhibition_no
    ) TEI_MAX ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
    AND TE.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
      AND TE.exhibition_classification_info->>'auctionClassification' = '2'
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND now() < TEI_MAX.extended_end_datetime
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
combined_categories AS (
  SELECT item_no, free_field->>'category' AS category_code
  FROM t_item_localized
  WHERE tenant_no = 1
    AND language_code = 'en'
    AND (delete_flag IS NULL OR delete_flag = 0)
),
filter_item AS (
  SELECT TEI.exhibition_no,
          TEI.exhibition_item_no,
          array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
  FROM t_exhibition_item TEI
    JOIN filter_exhibition FE ON FE.exhibition_no = TEI.exhibition_no
    JOIN t_lot TL
      ON TL.lot_no = TEI.lot_no
      AND TL.tenant_no = 1
      AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
      AND TLD.tenant_no = 1
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
      AND TI.tenant_no = 1
      AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    JOIN t_item_localized TIL
      ON TIL.item_no = TI.item_no
      AND TIL.tenant_no = 1
      AND TIL.language_code = 'en'
      AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
    LEFT JOIN t_exhibition_item_favorite TEIF
      ON TEIF.exhibition_item_no = TEI.exhibition_item_no
      AND TEIF.tenant_no = 1
      AND TEIF.member_no = 10
    LEFT JOIN t_bid TB
      ON TB.exhibition_item_no = TEI.exhibition_item_no
      AND TB.tenant_no = 1
      AND TB.member_no = 10
    JOIN combined_categories CC ON CC.item_no = TI.item_no
  WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
    AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
    AND TEI.hummer_flag = 0
    AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
    AND TEI.exhibition_item_no IS NOT NULL
  GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
)
-- Get array of exhibition_item_nos for the bid status function
SELECT array_agg(FI.exhibition_item_no) as exhibition_item_nos FROM filter_item FI;

-- Query 13: Call bid status function (run this after getting exhibition_item_nos from Query 12)
-- Replace the array below with the result from Query 12
-- SELECT * FROM f_get_auction_item_bid_status(
--   ARRAY[/* paste exhibition_item_nos here */],
--   1, -- tenant_no
--   10 -- member_no
-- );

-- Query 14: Final items query with all data (simplified version without complex sorting)
-- This is a simplified version - you may need to adjust based on your specific needs
WITH filter_exhibition AS (
  SELECT TE.exhibition_no,
          TE.category_id,
          TE.pitch_width,
          TE.more_little_judge_pitch,
          TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
          TE.start_datetime,
          TE.end_datetime,
          TE.preview_end_datetime,
          TEI_MAX.extended_end_datetime,
          TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag=0
          AND TEI.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
        GROUP BY TEI.exhibition_no
    ) TEI_MAX ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
    AND TE.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
      AND TE.exhibition_classification_info->>'auctionClassification' = '2'
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND now() < TEI_MAX.extended_end_datetime
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
combined_categories AS (
  SELECT item_no, free_field->>'category' AS category_code
  FROM t_item_localized
  WHERE tenant_no = 1
    AND language_code = 'en'
    AND (delete_flag IS NULL OR delete_flag = 0)
),
filter_item AS (
  SELECT TEI.exhibition_no,
          TEI.exhibition_item_no,
          array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
  FROM t_exhibition_item TEI
    JOIN filter_exhibition FE ON FE.exhibition_no = TEI.exhibition_no
    JOIN t_lot TL
      ON TL.lot_no = TEI.lot_no
      AND TL.tenant_no = 1
      AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
      AND TLD.tenant_no = 1
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
      AND TI.tenant_no = 1
      AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    JOIN t_item_localized TIL
      ON TIL.item_no = TI.item_no
      AND TIL.tenant_no = 1
      AND TIL.language_code = 'en'
      AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
    LEFT JOIN t_exhibition_item_favorite TEIF
      ON TEIF.exhibition_item_no = TEI.exhibition_item_no
      AND TEIF.tenant_no = 1
      AND TEIF.member_no = 10
    LEFT JOIN t_bid TB
      ON TB.exhibition_item_no = TEI.exhibition_item_no
      AND TB.tenant_no = 1
      AND TB.member_no = 10
    JOIN combined_categories CC ON CC.item_no = TI.item_no
  WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
    AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
    AND TEI.hummer_flag = 0
    AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
    AND TEI.exhibition_item_no IS NOT NULL
  GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
),
count_lot_items AS (
  SELECT TLD.lot_no, count(*) as count
    FROM t_lot_detail TLD
    JOIN t_exhibition_item TEI ON TEI.lot_no = TLD.lot_no
    JOIN filter_exhibition FE ON FE.exhibition_no = TEI.exhibition_no
    WHERE TLD.tenant_no = 1
    GROUP BY TLD.lot_no
),
items AS (
  SELECT  json_build_object(
            'item_no', TI.item_no,
            'manage_no', TI.manage_no,
            'exhibition_item_no', TEI.exhibition_item_no,
            'exhibition_no', TEI.exhibition_no,
            'auction_classification', FE.auction_classification,
            'category_id', FE.category_id,
            'lot_id', TL.lot_id,
            'item_count', CLI.count,
            'free_field', TIL.free_field,
            'image', (
                SELECT ARRAY[json_build_object('file_path', TIAF.file_path)] FROM t_item_ancillary_file TIAF
                WHERE TIAF.manage_no = TI.manage_no
                  AND TIAF.tenant_no = 1
                  AND TIAF.item_no = TI.item_no
                  AND TIAF.division = 1
                  AND TIAF.delete_flag = 0
                  AND TIAF.postar_file_path IS NULL
                ORDER BY TIAF.serial_number
                LIMIT 1
              ),
            'pdfs', (
                SELECT ARRAY[json_build_object('file_path', TIAF.file_path)] FROM t_item_ancillary_file TIAF
                WHERE TIAF.manage_no = TI.manage_no
                  AND TIAF.tenant_no = 1
                  AND TIAF.item_no = TI.item_no
                  AND TIAF.division = 2
                  AND TIAF.delete_flag = 0
                LIMIT 1
              ),
            'is_recommending', (TI.status = 2 AND now() BETWEEN TI.recommend_start_datetime AND TI.recommend_end_datetime),
            'sold_out', TEI.hummer_flag = 1,
            'start_datetime', TEI.start_datetime,
            'end_datetime', FE.extended_end_datetime,
            'create_datetime', TEI.create_datetime
          )::jsonb as item,
          ROW_NUMBER() OVER (ORDER BY TI.manage_no ASC) AS row_number
    FROM t_exhibition_item TEI
    JOIN filter_exhibition FE ON FE.exhibition_no = TEI.exhibition_no
    JOIN t_lot TL
      ON TL.lot_no = TEI.lot_no
      AND TL.tenant_no = 1
      AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
      AND TLD.tenant_no = 1
      AND TLD.order_no = 1
    LEFT JOIN count_lot_items CLI ON CLI.lot_no = TEI.lot_no
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
      AND TI.tenant_no = 1
      AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    JOIN t_item_localized TIL
      ON TIL.item_no = TI.item_no
      AND TIL.tenant_no = 1
      AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      AND TIL.language_code = 'en'
    JOIN filter_item FI ON TEI.exhibition_item_no = FI.exhibition_item_no
    WHERE TEI.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
)
SELECT * FROM items LIMIT 20;
