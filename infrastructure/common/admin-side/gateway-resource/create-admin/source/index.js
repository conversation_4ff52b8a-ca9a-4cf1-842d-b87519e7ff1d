const Define = require(process.env.COMMON_LAYER_PATH + 'define')
const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js')
const Base = require(process.env.COMMON_LAYER_PATH + 'base.js')
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js')
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const {
  AdminAddUserToGroupCommand,
  AdminCreateUserCommand,
  AdminSetUserPasswordCommand,
  CognitoIdentityProviderClient,
} = require('@aws-sdk/client-cognito-identity-provider')

const pool = new PgPool()
const cognitoClient = new CognitoIdentityProviderClient({})

/**
 * Validates user input, checking for password confirmation and existing login ID.
 */
async function validateInput(params, tenant_no) {
  await Validator.validation(params.data).catch(error => {
    throw {
      status: 406,
      name: 'Error by Validator.validation',
      message: error.errors,
    }
  })

  if (params.data.password !== params.data.password_confirm) {
    throw {
      status: 400,
      name: 'Error by password and password_confirm not match',
      message: Define.MESSAGE.E000129,
    }
  }

  const sql = Define.QUERY.GET_ADMIN_BY_LOGIN_ID_FUNCTION
  const response = await pool.rlsQuery(tenant_no, sql, [
    params.data.login_id,
    tenant_no,
  ])

  if (response && response.length > 0) {
    throw {
      status: 400,
      name: 'Login ID Exists',
      message: Define.MESSAGE.E000131,
    }
  }
}

/**
 * Inserts a new admin record into the database.
 */
async function createAdminInDb(
  admin,
  tenant_no,
  login_admin_no,
  hash_password
) {
  const result = await pool.rlsQuery(
    tenant_no,
    'SELECT f_insert_or_update_admin($1, $2, $3, $4, $5, $6, $7, $8) AS admin_no',
    [
      tenant_no,
      admin.admin_name,
      admin.login_id,
      hash_password,
      admin.role_id,
      login_admin_no,
      login_admin_no,
      admin.delete_flag,
    ]
  )

  const createdAdminNo = result[0]?.admin_no
  if (!createdAdminNo) {
    throw {
      status: 400,
      name: 'Error by f_insert_or_update_admin',
      message: 'Unexpected database response structure',
    }
  }
  return createdAdminNo
}

/**
 * Creates a new user in the Cognito user pool.
 */
async function createCognitoUser(userData, createdAdminNo) {
  const command = new AdminCreateUserCommand({
    UserPoolId: process.env.COGNITO_USER_POOL_ID,
    Username: userData.login_id,
    UserAttributes: [
      {Name: 'email', Value: userData.login_id},
      {Name: 'email_verified', Value: 'true'},
      {Name: 'custom:admin_no', Value: String(createdAdminNo)},
      {Name: 'custom:admin_language_code', Value: 'ja'},
      {Name: 'custom:role_id', Value: String(userData.role_id)},
      {Name: 'custom:admin_name', Value: userData.admin_name},
    ],
    TemporaryPassword: userData.password,
    MessageAction: 'SUPPRESS',
  })
  const createUserResult = await cognitoClient.send(command)
  console.log('User created in Cognito:', createUserResult)
  return createUserResult
}

/**
 * Sets the user's password to permanent in Cognito.
 */
async function setCognitoPassword(username, password) {
  const command = new AdminSetUserPasswordCommand({
    UserPoolId: process.env.COGNITO_USER_POOL_ID,
    Username: username,
    Password: password,
    Permanent: true,
  })
  await cognitoClient.send(command)
  console.log('Password set to permanent for user:', username)
}

/**
 * Adds the Cognito user to the tenant-specific group.
 */
async function addUserToCognitoGroup(username, tenant_no) {
  const group = `${Common.getCognitoGroupPrefix()}${tenant_no}`
  const command = new AdminAddUserToGroupCommand({
    UserPoolId: process.env.COGNITO_USER_POOL_ID,
    Username: username,
    GroupName: group,
  })
  await cognitoClient.send(command)
  console.log(`User ${username} added to group ${group}`)
  return group
}

/**
 * Rolls back the admin creation from the database if an error occurs.
 */
async function rollbackAdminCreation(admin_no, tenant_no) {
  if (!admin_no) return
  try {
    const sql_check =
      'SELECT 1 FROM m_admin WHERE admin_no = $1 AND tenant_no = $2'
    const exists = await pool.rlsQuery(tenant_no, sql_check, [
      admin_no,
      tenant_no,
    ])
    console.log('[🧯 TRACE LOG] is admin created: ', exists.length > 0, exists)
    if (exists.length > 0) {
      const sql_delete =
        'DELETE FROM m_admin WHERE admin_no = $1 AND tenant_no = $2'
      await pool.rlsQuery(tenant_no, sql_delete, [admin_no, tenant_no])
      console.log('Successfully rolled back admin creation!')
    }
  } catch (rollbackError) {
    console.error('Error during database rollback:', rollbackError)
  }
}

/**
 * Formats and returns an error response.
 */
function handleError(error) {
  const cognitoErrors = [
    'UsernameExistsException',
    'InvalidPasswordException',
    'InvalidParameterException',
    'AccessDeniedException',
  ]

  if (error.status === 400 || error.status === 406) {
    return {
      status: error.status,
      name: error.name || 'Validation/Bad Request Error',
      message: error.message || Define.MESSAGE.E900001,
    }
  }

  if (cognitoErrors.includes(error.name)) {
    return {
      status: 500,
      name: 'Cognito Error',
      message: error.message || Define.MESSAGE.E900001,
    }
  }

  return {
    status: 500,
    name: 'Unknown Error',
    message: error.message || Define.MESSAGE.E900001,
  }
}

/**
 * Main logic for creating an admin, now using async/await.
 */
async function createAdmin(e) {
  const params = Base.parseRequestBody(e.body)
  console.log('params = ' + JSON.stringify(params))
  const tenant_no = Base.extractTenantId(e)
  console.log('tenant_no = ' + tenant_no)

  await Base.startRequest(e)
  await Base.checkAccessIpAddress(
    pool,
    e.requestContext.identity.sourceIp,
    tenant_no
  )
  await validateInput(params, tenant_no)

  const hash_password = await Base.hashPassword(params.data.password)
  const login_admin_no = Base.extractAdminNo(e)
  const adminData = params.data

  const createdAdminNo = await createAdminInDb(
    adminData,
    tenant_no,
    login_admin_no,
    hash_password
  )
  console.log('log of createdAdminNo: ', createdAdminNo)

  // Attach admin_no to the error object for the catch block
  try {
    const createUserResult = await createCognitoUser(adminData, createdAdminNo)
    const username = createUserResult.User.Username
    await setCognitoPassword(username, adminData.password)
    const group = await addUserToCognitoGroup(username, tenant_no)

    return {
      message: 'User registered successfully',
      username: username,
      userSub: createUserResult.User.Username,
      group: group,
      email: adminData.login_id,
      tenantId: tenant_no,
      role: adminData.role_id,
      adminName: adminData.admin_name,
    }
  } catch (error) {
    error.createdAdminNo = createdAdminNo
    throw error
  }
}

/**
 * Lambda handler function.
 */
exports.handle = (e, ctx, cb) => {
  ctx.callbackWaitsForEmptyEventLoop = false
  console.log('create-admin: ', e)

  createAdmin(e)
    .then(user => Base.createSuccessResponse(cb, user))
    .catch(async error => {
      console.error('Occurred error during registration:', error)
      if (error.createdAdminNo) {
        const tenant_no = Base.extractTenantId(e)
        await rollbackAdminCreation(error.createdAdminNo, tenant_no)
      }
      const errorResponse = handleError(error)
      return Base.createErrorResponse(cb, errorResponse)
    })
}
