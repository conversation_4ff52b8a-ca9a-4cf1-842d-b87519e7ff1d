const {queryAnalyze} = require('./query-analize')

/* eslint-disable camelcase */
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.READ_ONLY_PGHOST)
const writePool = new PgPool(process.env.PGHOST)

exports.handle = (e, ctx, cb) => {
  console.log('📚 log of event : ', e)
  const params = e.body
  const header = e.headers
  // TODO: get member_no from cognito authorizer
  const authorizer = {member_no: '10'}
  const base = new Base(pool, params.languageCode)
  const exhibitionNos = []

  queryAnalyze(pool)

  const getAuctionClassification = classification => {
    if (classification === null) {
      return null
    }
    if (classification === 'ascending') {
      return 1
    }
    return 2
  }

  console.log(
    'log:  auctionClassification: ',
    getAuctionClassification(params.auction_classification)
  )
  const tenant = {tenant_no: 1}

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      console.log('📝 log of tenant info : ', tenant)
      if (params.searchKey) {
        return writePool.rlsQuery(
          tenant.tenant_no,
          'select * from f_insert_member_search_history($1,$2,$3);',
          [tenant.tenant_no, params.searchKey, authorizer.member_no]
        )
      }
      return Promise.resolve()
    })
    .then(() => {
      if (params.exhibitionNos && params.exhibitionNos.length > 0) {
        params.exhibitionNos.map(x => exhibitionNos.push(x))
        return Promise.resolve()
      }
      return pool
        .rlsQuery(
          tenant.tenant_no,
          `SELECT
            exhibition_no, preview_end_datetime
            FROM t_exhibition
            WHERE delete_flag= 0 AND (status = 0 OR preview_end_datetime >= now ());`
        )
        .then(res => {
          console.table(res)
          res.map(({exhibition_no}) => exhibitionNos.push(exhibition_no))
          return Promise.resolve()
        })
    })
    .then(() => {
      let in_search_results_sort = null

      // 商品を並び替える
      if (params.sorter) {
        switch (params.sorter.column) {
          case 'sorted_by_recommended_item':
            in_search_results_sort = 'recommend_sort'
            break
          case 'sorted_by_create_datetime':
            in_search_results_sort = params.sorter.asc
              ? 'create_datetime_asc'
              : 'create_datetime_desc'
            break
          case 'sorted_by_remaining_time':
            in_search_results_sort = params.sorter.asc
              ? 'remain_time_asc'
              : 'remain_time_desc'
            break
          case 'sorted_by_current_price':
            in_search_results_sort = params.sorter.asc
              ? 'current_price_asc'
              : 'current_price_desc'
            break
          case 'sorted_by_bid_count':
            in_search_results_sort = params.sorter.asc
              ? 'bid_count_asc'
              : 'bid_count_desc'
            break
          default:
            console.warn(`Unexpected sorter column: ${params.sorter.column}`)
        }
      }
      console.log('log of in_search_results_sort:', in_search_results_sort)
      const sql_params = [
        tenant.tenant_no,
        exhibitionNos,
        getAuctionClassification(params.auction_classification),
        params.searchKey ? [].concat(params.searchKey) : null,
        params.areas,
        null, // params.category
        params.startYear,
        params.endYear,
        params.startPrice,
        params.endPrice,
        params.favorite,
        params.bidding,
        params.unSoldOut,
        params.recommending,
        params.exceedingLowestPrice,
        in_search_results_sort,
        params.categorySort,
        params.endDatetimeNotExtendSort,
        params.endedDatetimeSort,
        params.nicknameSort,
        params.limit,
        params.showedItemNos,
        params.exhibitionItemNos,
        base.language,
        authorizer.member_no,
        params.modelList,
        params.categoryList,
        null,
        null,
        params.brandList,
      ]
      console.log('sql_params before query f_search_auction_items:', sql_params)
      const sql = `SELECT * FROM "f_search_auction_items"(${Common.sqlParamNumber(30)});`
      return pool.rlsQuery(tenant.tenant_no, sql, sql_params)
    })

    .then(result => {
      console.log('☎️ log of query f_search_auction_items : ', result)
      const data = result.length > 0 && result[0].data ? result[0].data : {}
      const items = data.items ? data.items : []
      const isMoreLimit = params.limit && items.length > params.limit
      const response = {
        items,
        count: data.count,
        count_init: data.count_init, // Counting all items when searching by initial conditions
        category_group: data.category_group ? data.category_group : [],
        exhibition_group: data.exhibition_group ? data.exhibition_group : [],
        isMoreLimit,
        moreSearch: params.showedItemNos && params.showedItemNos.length > 0,
      }
      return base.createSuccessResponse(cb, response)
    })
    .catch(error => base.createErrorResponse(cb, error))
}
