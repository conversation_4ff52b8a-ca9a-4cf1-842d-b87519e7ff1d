export const getEscapeKeywords = async (pool, tenantId, in_search_keys) => {
  const sql = `
    WITH escapedKeywords AS (
    SELECT f_escape_string(keyword) AS search_keys FROM unnest($1) AS keyword
    )
    SELECT search_key FROM escaped_keywords
  `

  const {rows} = await pool.rlsQuery(tenantId, sql, [in_search_keys])
  return rows.map(row => row.search_key)
}

export const productCategoryExcludeOtherAndGetEscapeKeywords = async (
  pool,
  tenantId,
  in_lang_code,
  category_other
) => {
  const sql = `

    WITH escapedKeywords AS (
    SELECT f_escape_string(keyword) AS search_keys FROM unnest($1) AS keyword
    ),
    WITH productCategoryExcludeOther AS(
      SELECT mcl.value1
                , mcl.value2
                , unnest(regexp_split_to_array(value3,',')) as value3
      FROM m_constant mc
        INNER JOIN m_constant_localized mcl
            ON mcl.constant_no = mc.constant_no
      WHERE mc.tenant_no = 1
        AND mcl.language_code = $1
        AND mc.key_string = 'PRODUCT_CATEGORY'
        AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
        AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
        AND mcl.value1 <> $2
    )
    SELECT search_key FROM escaped_keywords
  `

  const {rows} = await pool.rlsQuery(tenantId, sql, [
    in_lang_code,
    category_other,
  ])
  return rows.map(row => row.search_key)
}


export const queryAnalyze = async pool => {
  const tenantId = 1
  const in_search_keys = ''
  const in_lang_code = 'ja'
  const category_other = '3'

  const escapedKeywords = await getEscapeKeywords(
    pool,
    tenantId,
    in_search_keys
  )
  console.log('🗑 log of escapedKeywords : ', escapedKeywords)

  const productCategoryExcludeOtherAndGetEscapeKeywords = await productCategoryExcludeOtherAndGetEscapeKeywords(
    pool,
    tenantId,
    in_lang_code,
    category_other
  )
}
