// Progressive CTE Analysis Pipeline for search-auction-items debugging
// Each stage builds incrementally on the previous one

// Common parameters used across all stages
const getCommonParams = () => ({
  in_tenant_no: 1,
  in_exhibition_nos: [
    4, 5, 67, 100, 34, 133, 200, 166, 201, 134, 232, 199, 265, 298, 202, 299,
    203, 331, 332,
  ],
  in_auction_classification: '2',
  in_search_keys: null,
  in_areas: undefined,
  in_category: null,
  in_start_year: undefined,
  in_end_year: undefined,
  in_start_price: undefined,
  in_end_price: undefined,
  in_favorite: undefined,
  in_bidding: undefined,
  in_un_sold_out: undefined,
  in_recommending: undefined,
  in_exceeding_lowest_price: undefined,
  in_search_results_sort: null,
  in_category_sort: undefined,
  in_end_datetime_not_extend_sort: undefined,
  in_ended_datetime_sort: undefined,
  in_nickname_sort: undefined,
  in_limit: undefined,
  in_showed_item_nos: undefined,
  in_exhibition_item_nos: undefined,
  in_lang_code: 'en',
  in_member_no: 10,
  in_model_list: undefined,
  in_categories: undefined,
  in_brand: null,
  in_detail_item_no: null,
  in_brands: undefined,
})

// STAGE 1: Execute only escapedKeywords CTE
const analyzeStage1 = async pool => {
  console.log('🚀 STAGE 1: Analyzing escapedKeywords CTE')
  console.log('='.repeat(60))

  const params = getCommonParams()
  const tenantId = params.in_tenant_no

  try {
    const sql = `
      SELECT f_escape_string(keyword) AS search_keys
      FROM unnest($1) AS keyword
    `

    console.log('📊 Input Parameters:')
    console.log('  - in_search_keys:', params.in_search_keys)
    console.log('  - tenantId:', tenantId)

    const {rows} = await pool.rlsQuery(tenantId, sql, [params.in_search_keys])

    console.log('📋 Results:')
    console.log('  - Row count:', rows.length)
    console.log('  - Sample data:', rows.slice(0, 5))
    console.log('  - All results:', rows)

    return {
      stage: 1,
      cte_name: 'escapedKeywords',
      row_count: rows.length,
      results: rows,
      success: true,
    }
  } catch (error) {
    console.error('❌ Stage 1 Error:', error)
    return {
      stage: 1,
      cte_name: 'escapedKeywords',
      row_count: 0,
      results: [],
      success: false,
      error: error.message,
    }
  }
}

// STAGE 2: Execute escapedKeywords + productCategoryExcludeOther CTEs
const analyzeStage2 = async pool => {
  console.log(
    '🚀 STAGE 2: Analyzing escapedKeywords + productCategoryExcludeOther CTEs'
  )
  console.log('='.repeat(60))

  const params = getCommonParams()
  const tenantId = params.in_tenant_no
  const category_other = '3'

  try {
    // First CTE: escapedKeywords
    const stage1Result = await analyzeStage1(pool)

    // Second CTE: productCategoryExcludeOther
    const sql2 = `
      SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(value3,',')) as value3
      FROM m_constant mc
        INNER JOIN m_constant_localized mcl
            ON mcl.constant_no = mc.constant_no
      WHERE mc.tenant_no = $1
        AND mcl.language_code = $2
        AND mc.key_string = 'PRODUCT_CATEGORY'
        AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
        AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
        AND mcl.value1 <> $3
    `

    console.log('📊 Input Parameters for productCategoryExcludeOther:')
    console.log('  - in_tenant_no:', params.in_tenant_no)
    console.log('  - in_lang_code:', params.in_lang_code)
    console.log('  - category_other:', category_other)

    const {rows: rows2} = await pool.rlsQuery(tenantId, sql2, [
      params.in_tenant_no,
      params.in_lang_code,
      category_other,
    ])

    console.log('📋 productCategoryExcludeOther Results:')
    console.log('  - Row count:', rows2.length)
    console.log('  - Sample data:', rows2.slice(0, 5))

    return {
      stage: 2,
      ctes: ['escapedKeywords', 'productCategoryExcludeOther'],
      results: {
        escapedKeywords: stage1Result,
        productCategoryExcludeOther: {
          row_count: rows2.length,
          results: rows2,
        },
      },
      success: true,
    }
  } catch (error) {
    console.error('❌ Stage 2 Error:', error)
    return {
      stage: 2,
      success: false,
      error: error.message,
    }
  }
}

// STAGE 3: Add productCategoryIncludeOther CTE
const analyzeStage3 = async pool => {
  console.log('🚀 STAGE 3: Adding productCategoryIncludeOther CTE')
  console.log('='.repeat(60))

  const params = getCommonParams()
  const tenantId = params.in_tenant_no

  try {
    // Previous stages
    const stage2Result = await analyzeStage2(pool)

    // Third CTE: productCategoryIncludeOther
    const sql3 = `
      SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(value3,',')) as value3
      FROM m_constant mc
        INNER JOIN m_constant_localized mcl
            ON mcl.constant_no = mc.constant_no
      WHERE mc.tenant_no = $1
        AND mcl.language_code = $2
        AND mc.key_string = 'PRODUCT_CATEGORY'
        AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
        AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
    `

    console.log('📊 Input Parameters for productCategoryIncludeOther:')
    console.log('  - in_tenant_no:', params.in_tenant_no)
    console.log('  - in_lang_code:', params.in_lang_code)

    const {rows: rows3} = await pool.rlsQuery(tenantId, sql3, [
      params.in_tenant_no,
      params.in_lang_code,
    ])

    console.log('📋 productCategoryIncludeOther Results:')
    console.log('  - Row count:', rows3.length)
    console.log('  - Sample data:', rows3.slice(0, 5))

    return {
      stage: 3,
      cte_names: [
        'escapedKeywords',
        'productCategoryExcludeOther',
        'productCategoryIncludeOther',
      ],
      results: {
        ...stage2Result.results,
        productCategoryIncludeOther: {
          row_count: rows3.length,
          results: rows3,
        },
      },
      success: true,
    }
  } catch (error) {
    console.error('❌ Stage 3 Error:', error)
    return {
      stage: 3,
      success: false,
      error: error.message,
    }
  }
}

// STAGE 4: Add combined_categories CTE
const analyzeStage4 = async pool => {
  console.log('🚀 STAGE 4: Adding combined_categories CTE')
  console.log('='.repeat(60))

  const params = getCommonParams()
  const tenantId = params.in_tenant_no

  try {
    // Previous stages
    const stage3Result = await analyzeStage3(pool)

    // Fourth CTE: combined_categories
    const sql4 = `
      SELECT item_no, free_field->>'category' AS category_code
      FROM t_item_localized
      WHERE tenant_no = $1
        AND language_code = $2
        AND (delete_flag IS NULL OR delete_flag = 0)
    `

    console.log('📊 Input Parameters for combined_categories:')
    console.log('  - in_tenant_no:', params.in_tenant_no)
    console.log('  - in_lang_code:', params.in_lang_code)

    const {rows: rows4} = await pool.rlsQuery(tenantId, sql4, [
      params.in_tenant_no,
      params.in_lang_code,
    ])

    console.log('📋 combined_categories Results:')
    console.log('  - Row count:', rows4.length)
    console.log('  - Sample data:', rows4.slice(0, 5))

    return {
      stage: 4,
      cte_names: [
        'escapedKeywords',
        'productCategoryExcludeOther',
        'productCategoryIncludeOther',
        'combined_categories',
      ],
      results: {
        ...stage3Result.results,
        combined_categories: {
          row_count: rows4.length,
          results: rows4.slice(0, 10), // Limit sample for large datasets
        },
      },
      success: true,
    }
  } catch (error) {
    console.error('❌ Stage 4 Error:', error)
    return {
      stage: 4,
      success: false,
      error: error.message,
    }
  }
}

// STAGE 5: Add filter_exhibition CTE (CRITICAL - filters exhibitions)
const analyzeStage5 = async pool => {
  console.log('🚀 STAGE 5: Adding filter_exhibition CTE (CRITICAL)')
  console.log('='.repeat(60))

  const params = getCommonParams()
  const tenantId = params.in_tenant_no

  try {
    // Previous stages
    const stage4Result = await analyzeStage4(pool)

    // Fifth CTE: filter_exhibition - This is where exhibition filtering happens
    const sql5 = `
      SELECT TE.exhibition_no,
              TE.category_id,
              TE.pitch_width,
              TE.more_little_judge_pitch,
              TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
              TE.start_datetime,
              TE.end_datetime,
              TE.preview_end_datetime,
              TEI_MAX.extended_end_datetime,
              TEL.exhibition_name
        FROM t_exhibition TE
        JOIN (
            SELECT TEI.exhibition_no,
                    MAX(CASE
                          WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                          ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                      END) as extended_end_datetime
              FROM t_exhibition_item TEI
            WHERE TEI.delete_flag=0
              AND ($2 IS NULL OR TEI.exhibition_no = ANY($2))
              AND ($3 IS NULL OR TEI.exhibition_item_no::text = ANY($3))
            GROUP BY TEI.exhibition_no
        ) TEI_MAX
          ON TEI_MAX.exhibition_no = TE.exhibition_no
        LEFT JOIN t_exhibition_member TEM
          ON TEM.exhibition_no = TE.exhibition_no
          AND TEM.member_no = $4
        LEFT JOIN t_exhibition_localized TEL
          ON TEL.exhibition_no = TE.exhibition_no
          AND TEL.language_code = $5
        WHERE TE.tenant_no = $1
        AND ($2 IS NULL OR TE.exhibition_no = ANY($2))
          AND ($2 IS NOT NULL OR TE.status = 0)
          AND ($6 IS NULL OR TE.exhibition_classification_info->>'auctionClassification' = $6)
          AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
          AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
          AND ($2 IS NOT NULL OR now() < TEI_MAX.extended_end_datetime)
          AND (
                $7 IS NULL
                OR array_length($7, 1) IS NULL
                OR TE.category_id = ANY($7)
              )
          AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
    `

    console.log('📊 Input Parameters for filter_exhibition:')
    console.log('  - in_tenant_no:', params.in_tenant_no)
    console.log('  - in_exhibition_nos:', params.in_exhibition_nos)
    console.log('  - in_exhibition_item_nos:', params.in_exhibition_item_nos)
    console.log('  - in_member_no:', params.in_member_no)
    console.log('  - in_lang_code:', params.in_lang_code)
    console.log(
      '  - in_auction_classification:',
      params.in_auction_classification
    )
    console.log('  - in_areas:', params.in_areas)

    const {rows: rows5} = await pool.rlsQuery(tenantId, sql5, [
      params.in_tenant_no,
      params.in_exhibition_nos,
      params.in_exhibition_item_nos,
      params.in_member_no,
      params.in_lang_code,
      params.in_auction_classification,
      params.in_areas,
    ])

    console.log('📋 filter_exhibition Results:')
    console.log('  - Row count:', rows5.length)
    console.log('  - Sample data:', rows5.slice(0, 3))
    console.log(
      '🚨 CRITICAL: This CTE filters exhibitions - if 0 rows, no items will be found!'
    )

    return {
      stage: 5,
      cte_names: [...stage4Result.cte_names, 'filter_exhibition'],
      results: {
        ...stage4Result.results,
        filter_exhibition: {
          row_count: rows5.length,
          results: rows5,
        },
      },
      success: true,
    }
  } catch (error) {
    console.error('❌ Stage 5 Error:', error)
    return {
      stage: 5,
      success: false,
      error: error.message,
    }
  }
}

// STAGE 6: Add count_all_initial CTE (Shows initial item count before filtering)
const analyzeStage6 = async pool => {
  console.log('🚀 STAGE 6: Adding count_all_initial CTE (Initial Count)')
  console.log('='.repeat(60))

  const params = getCommonParams()
  const tenantId = params.in_tenant_no

  try {
    // Previous stages
    const stage5Result = await analyzeStage5(pool)

    // Check if we have exhibitions to work with
    if (stage5Result.results.filter_exhibition.row_count === 0) {
      console.log(
        '🚨 WARNING: No exhibitions found in filter_exhibition - count_all_initial will be 0'
      )
      return {
        stage: 6,
        cte_names: [...stage5Result.cte_names, 'count_all_initial'],
        results: {
          ...stage5Result.results,
          count_all_initial: {
            row_count: 1,
            results: [{count: 0}],
          },
        },
        success: true,
      }
    }

    // Sixth CTE: count_all_initial - Count items with initial conditions
    const sql6 = `
      WITH temp_filter_exhibition AS (
        SELECT * FROM (VALUES ${stage5Result.results.filter_exhibition.results
          .map(
            (_, i) =>
              `($${i * 10 + 1}, $${i * 10 + 2}, $${i * 10 + 3}, $${i * 10 + 4}, $${i * 10 + 5}::text, $${i * 10 + 6}::timestamp, $${i * 10 + 7}::timestamp, $${i * 10 + 8}::timestamp, $${i * 10 + 9}::timestamp, $${i * 10 + 10}::text)`
          )
          .join(
            ', '
          )}) AS t(exhibition_no, category_id, pitch_width, more_little_judge_pitch, auction_classification, start_datetime, end_datetime, preview_end_datetime, extended_end_datetime, exhibition_name)
      )
      SELECT COUNT(*) as count
        FROM (
          SELECT TEI.exhibition_item_no,
                array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
            FROM t_exhibition_item TEI
            JOIN temp_filter_exhibition FE
              ON FE.exhibition_no = TEI.exhibition_no
            JOIN t_lot TL
              ON TL.lot_no = TEI.lot_no
            AND TL.tenant_no = $${stage5Result.results.filter_exhibition.results.length * 10 + 1}
            AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
            JOIN t_lot_detail TLD
              ON TLD.lot_no = TEI.lot_no
            AND TLD.tenant_no = $${stage5Result.results.filter_exhibition.results.length * 10 + 1}
            JOIN t_item TI
              ON TLD.item_no = TI.item_no
            AND TI.tenant_no = $${stage5Result.results.filter_exhibition.results.length * 10 + 1}
            AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
            JOIN t_item_localized TIL
              ON TIL.item_no = TI.item_no
            AND TIL.tenant_no = $${stage5Result.results.filter_exhibition.results.length * 10 + 1}
            AND TIL.language_code = $${stage5Result.results.filter_exhibition.results.length * 10 + 2}
            AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
          WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
            AND ($${stage5Result.results.filter_exhibition.results.length * 10 + 3} IS NOT NULL OR TEI.hummer_flag = 0)
            AND ($${stage5Result.results.filter_exhibition.results.length * 10 + 3} IS NOT NULL OR now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime))
            AND TEI.exhibition_item_no IS NOT NULL
            GROUP BY TEI.exhibition_item_no
        ) CAT
    `

    const tempParams = stage5Result.results.filter_exhibition.results.flatMap(
      row => [
        row.exhibition_no,
        row.category_id,
        row.pitch_width,
        row.more_little_judge_pitch,
        row.auction_classification,
        row.start_datetime,
        row.end_datetime,
        row.preview_end_datetime,
        row.extended_end_datetime,
        row.exhibition_name,
      ]
    )

    const finalParams = [
      ...tempParams,
      params.in_tenant_no,
      params.in_lang_code,
      params.in_exhibition_nos,
    ]

    console.log('📊 Input Parameters for count_all_initial:')
    console.log(
      '  - Using filter_exhibition results:',
      stage5Result.results.filter_exhibition.row_count,
      'exhibitions'
    )
    console.log('  - in_tenant_no:', params.in_tenant_no)
    console.log('  - in_lang_code:', params.in_lang_code)
    console.log('  - in_exhibition_nos:', params.in_exhibition_nos)

    const {rows: rows6} = await pool.rlsQuery(tenantId, sql6, finalParams)

    console.log('📋 count_all_initial Results:')
    console.log('  - Initial item count:', rows6[0]?.count || 0)
    console.log('🔍 This shows how many items exist before main filtering')

    return {
      stage: 6,
      cte_names: [...stage5Result.cte_names, 'count_all_initial'],
      results: {
        ...stage5Result.results,
        count_all_initial: {
          row_count: 1,
          results: rows6,
        },
      },
      success: true,
    }
  } catch (error) {
    console.error('❌ Stage 6 Error:', error)
    return {
      stage: 6,
      success: false,
      error: error.message,
    }
  }
}

// STAGE 7: Add filter_item CTE (MOST CRITICAL - This is where items get filtered out!)
const analyzeStage7 = async pool => {
  console.log(
    '🚀 STAGE 7: Adding filter_item CTE (MOST CRITICAL - Main Item Filtering)'
  )
  console.log('='.repeat(60))

  const params = getCommonParams()
  const tenantId = params.in_tenant_no

  try {
    // Previous stages
    const stage6Result = await analyzeStage6(pool)

    // Check if we have exhibitions and initial count
    if (stage6Result.results.filter_exhibition.row_count === 0) {
      console.log('🚨 ERROR: No exhibitions found - filter_item will be empty')
      return {
        stage: 7,
        cte_names: [...stage6Result.cte_names, 'filter_item'],
        results: {
          ...stage6Result.results,
          filter_item: {
            row_count: 0,
            results: [],
          },
        },
        success: true,
      }
    }

    const initialCount =
      stage6Result.results.count_all_initial.results[0]?.count || 0
    console.log(`🔍 Initial item count from Stage 6: ${initialCount}`)

    // This is a simplified version of filter_item for debugging
    // In production, this CTE has many complex WHERE conditions
    const sql7 = `
      WITH temp_filter_exhibition AS (
        SELECT * FROM (VALUES ${stage6Result.results.filter_exhibition.results
          .map(
            (_, i) =>
              `($${i * 10 + 1}, $${i * 10 + 2}, $${i * 10 + 3}, $${i * 10 + 4}, $${i * 10 + 5}::text, $${i * 10 + 6}::timestamp, $${i * 10 + 7}::timestamp, $${i * 10 + 8}::timestamp, $${i * 10 + 9}::timestamp, $${i * 10 + 10}::text)`
          )
          .join(
            ', '
          )}) AS t(exhibition_no, category_id, pitch_width, more_little_judge_pitch, auction_classification, start_datetime, end_datetime, preview_end_datetime, extended_end_datetime, exhibition_name)
      )
      SELECT TEI.exhibition_no,
              TEI.exhibition_item_no,
              array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
      FROM t_exhibition_item TEI
        JOIN temp_filter_exhibition FE
          ON FE.exhibition_no = TEI.exhibition_no
        JOIN t_lot TL
          ON TL.lot_no = TEI.lot_no
          AND TL.tenant_no = $${stage6Result.results.filter_exhibition.results.length * 10 + 1}
          AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
        JOIN t_lot_detail TLD
          ON TLD.lot_no = TEI.lot_no
          AND TLD.tenant_no = $${stage6Result.results.filter_exhibition.results.length * 10 + 1}
        JOIN t_item TI
          ON TLD.item_no = TI.item_no
          AND TI.tenant_no = $${stage6Result.results.filter_exhibition.results.length * 10 + 1}
          AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
        JOIN t_item_localized TIL
          ON TIL.item_no = TI.item_no
          AND TIL.tenant_no = $${stage6Result.results.filter_exhibition.results.length * 10 + 1}
          AND TIL.language_code = $${stage6Result.results.filter_exhibition.results.length * 10 + 2}
          AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
          AND ($${stage6Result.results.filter_exhibition.results.length * 10 + 3} IS NULL OR TIL.free_field->>'brand' = $${stage6Result.results.filter_exhibition.results.length * 10 + 3})
          AND ($${stage6Result.results.filter_exhibition.results.length * 10 + 4} IS NULL OR NOT TIL.item_no = $${stage6Result.results.filter_exhibition.results.length * 10 + 4})
        LEFT JOIN t_exhibition_item_favorite TEIF
          ON TEIF.exhibition_item_no = TEI.exhibition_item_no
          AND TEIF.tenant_no = $${stage6Result.results.filter_exhibition.results.length * 10 + 1}
          AND TEIF.member_no = $${stage6Result.results.filter_exhibition.results.length * 10 + 5}
        LEFT JOIN t_bid TB
          ON TB.exhibition_item_no = TEI.exhibition_item_no
          AND TB.tenant_no = $${stage6Result.results.filter_exhibition.results.length * 10 + 1}
          AND TB.member_no = $${stage6Result.results.filter_exhibition.results.length * 10 + 5}
      WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
        AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
        AND ($${stage6Result.results.filter_exhibition.results.length * 10 + 6} IS NOT NULL OR TEI.hummer_flag = 0)
        AND ($${stage6Result.results.filter_exhibition.results.length * 10 + 6} IS NOT NULL OR now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime))
        AND TEI.exhibition_item_no IS NOT NULL
      GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
    `

    const tempParams = stage6Result.results.filter_exhibition.results.flatMap(
      row => [
        row.exhibition_no,
        row.category_id,
        row.pitch_width,
        row.more_little_judge_pitch,
        row.auction_classification,
        row.start_datetime,
        row.end_datetime,
        row.preview_end_datetime,
        row.extended_end_datetime,
        row.exhibition_name,
      ]
    )

    const finalParams = [
      ...tempParams,
      params.in_tenant_no,
      params.in_lang_code,
      params.in_brand,
      params.in_detail_item_no,
      params.in_member_no,
      params.in_exhibition_nos,
    ]

    console.log('📊 Input Parameters for filter_item:')
    console.log(
      '  - Using filter_exhibition results:',
      stage6Result.results.filter_exhibition.row_count,
      'exhibitions'
    )
    console.log('  - in_tenant_no:', params.in_tenant_no)
    console.log('  - in_lang_code:', params.in_lang_code)
    console.log('  - in_brand:', params.in_brand)
    console.log('  - in_detail_item_no:', params.in_detail_item_no)
    console.log('  - in_member_no:', params.in_member_no)

    const {rows: rows7} = await pool.rlsQuery(tenantId, sql7, finalParams)

    console.log('📋 filter_item Results:')
    console.log('  - Filtered item count:', rows7.length)
    console.log('  - Sample data:', rows7.slice(0, 3))

    if (initialCount > 0 && rows7.length === 0) {
      console.log('🚨 CRITICAL ISSUE FOUND!')
      console.log(
        `  - Initial count was ${initialCount} but filter_item returned 0`
      )
      console.log('  - Items are being filtered out in the filter_item CTE!')
      console.log('  - Check the WHERE conditions in the filter_item CTE')
    } else if (rows7.length < initialCount) {
      console.log(
        `🔍 Items filtered: ${initialCount} → ${rows7.length} (${initialCount - rows7.length} items removed)`
      )
    }

    return {
      stage: 7,
      cte_names: [...stage6Result.cte_names, 'filter_item'],
      results: {
        ...stage6Result.results,
        filter_item: {
          row_count: rows7.length,
          results: rows7.slice(0, 10), // Limit for large datasets
        },
      },
      success: true,
    }
  } catch (error) {
    console.error('❌ Stage 7 Error:', error)
    return {
      stage: 7,
      success: false,
      error: error.message,
    }
  }
}

// Main queryAnalyze function - runs all stages progressively
const queryAnalyze = async pool => {
  console.log('🚀 Starting Progressive CTE Analysis Pipeline')
  console.log('='.repeat(80))

  try {
    // Run all stages progressively
    console.log('Running Stage 1...')
    const stage1 = await analyzeStage1(pool)

    console.log('\nRunning Stage 2...')
    const stage2 = await analyzeStage2(pool)

    console.log('\nRunning Stage 3...')
    const stage3 = await analyzeStage3(pool)

    console.log('\nRunning Stage 4...')
    const stage4 = await analyzeStage4(pool)

    console.log('\nRunning Stage 5 (CRITICAL - Exhibition Filtering)...')
    const stage5 = await analyzeStage5(pool)

    console.log('\nRunning Stage 6 (Initial Count)...')
    const stage6 = await analyzeStage6(pool)

    console.log('\nRunning Stage 7 (MOST CRITICAL - Item Filtering)...')
    const stage7 = await analyzeStage7(pool)

    // Final Summary
    console.log('\n🎯 FINAL ANALYSIS SUMMARY')
    console.log('='.repeat(80))
    console.log(
      `Stage 1 - escapedKeywords: ${stage1.success ? stage1.row_count + ' rows' : 'FAILED'}`
    )
    console.log(
      `Stage 2 - productCategoryExcludeOther: ${stage2.success ? stage2.results.productCategoryExcludeOther.row_count + ' rows' : 'FAILED'}`
    )
    console.log(
      `Stage 3 - productCategoryIncludeOther: ${stage3.success ? stage3.results.productCategoryIncludeOther.row_count + ' rows' : 'FAILED'}`
    )
    console.log(
      `Stage 4 - combined_categories: ${stage4.success ? stage4.results.combined_categories.row_count + ' rows' : 'FAILED'}`
    )
    console.log(
      `Stage 5 - filter_exhibition: ${stage5.success ? stage5.results.filter_exhibition.row_count + ' rows' : 'FAILED'}`
    )
    console.log(
      `Stage 6 - count_all_initial: ${stage6.success ? (stage6.results.count_all_initial.results[0]?.count || 0) + ' items' : 'FAILED'}`
    )
    console.log(
      `Stage 7 - filter_item: ${stage7.success ? stage7.results.filter_item.row_count + ' items' : 'FAILED'}`
    )

    const initialCount = stage6.success
      ? stage6.results.count_all_initial.results[0]?.count || 0
      : 0
    const finalCount = stage7.success ? stage7.results.filter_item.row_count : 0

    if (initialCount > 0 && finalCount === 0) {
      console.log('\n🚨 ROOT CAUSE IDENTIFIED:')
      console.log(`  - ${initialCount} items found initially`)
      console.log(`  - 0 items after filter_item CTE`)
      console.log('  - The issue is in the filter_item WHERE conditions!')
    } else if (finalCount < initialCount) {
      console.log(`\n🔍 FILTERING ANALYSIS:`)
      console.log(`  - Started with: ${initialCount} items`)
      console.log(`  - Ended with: ${finalCount} items`)
      console.log(`  - Items filtered out: ${initialCount - finalCount}`)
    }

    return {
      success: true,
      stages: [stage1, stage2, stage3, stage4, stage5, stage6, stage7],
      summary: {
        initial_count: initialCount,
        final_count: finalCount,
        items_filtered: initialCount - finalCount,
      },
    }
  } catch (error) {
    console.error('❌ Pipeline Error:', error)
    return {
      success: false,
      error: error.message,
    }
  }
}

// Export all functions using CommonJS
module.exports = {
  queryAnalyze,
  analyzeStage1,
  analyzeStage2,
  analyzeStage3,
  analyzeStage4,
  analyzeStage5,
  analyzeStage6,
  analyzeStage7,
  getCommonParams,
}
