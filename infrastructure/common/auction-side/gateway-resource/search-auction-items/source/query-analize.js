export const getEscapeKeywords = async (pool, tenantId, in_search_keys) => {
  const sql = `
    WITH escapedKeywords AS (
    SELECT f_escape_string(keyword) AS search_keys FROM unnest($1) AS keyword
    )
    SELECT search_key FROM escaped_keywords
  `

  const {rows} = await pool.rlsQuery(tenantId, sql, [in_search_keys])
  return rows.map(row => row.search_key)
}

export const productCategoryExcludeOther = async (
  pool,
  tenantId,
  in_lang_code,
  category_other
) => {
  const sql = `
    WITH productCategoryExcludeOther AS(
      SELECT mcl.value1
                , mcl.value2
                , unnest(regexp_split_to_array(value3,',')) as value3
      FROM m_constant mc
        INNER JOIN m_constant_localized mcl
            ON mcl.constant_no = mc.constant_no
      WHERE mc.tenant_no = 1
        AND mcl.language_code = $1
        AND mc.key_string = 'PRODUCT_CATEGORY'
        AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
        AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
        AND mcl.value1 <> $2
    )
    SELECT search_key FROM escaped_keywords
  `

  const {rows} = await pool.rlsQuery(tenantId, sql, [
    in_lang_code,
    category_other,
  ])
  return rows.map(row => row.search_key)
}
  const sql = ` WITH productCategoryIncludeOther AS (
  SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(value3,',')) as value3
  FROM m_constant mc
    INNER JOIN m_constant_localized mcl
        ON mcl.constant_no = mc.constant_no
  WHERE mc.tenant_no = 1
    AND mcl.language_code = in_lang_code
    AND mc.key_string = 'PRODUCT_CATEGORY'
    AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
    AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
),
`

  const sql = ` WITH combined_categories AS (
  SELECT item_no, free_field->>'category' AS category_code
  FROM t_item_localized
  WHERE tenant_no = in_tenant_no
    AND language_code = in_lang_code
    AND (delete_flag IS NULL OR delete_flag = 0)
),
`

  const sql = ` WITH filter_exhibition AS (
  SELECT TE.exhibition_no,
          TE.category_id,
          TE.pitch_width,
          TE.more_little_judge_pitch,
          TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
          TE.start_datetime,
          TE.end_datetime,
          TE.preview_end_datetime,
          TEI_MAX.extended_end_datetime,
          TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag=0
          AND (in_exhibition_nos IS NULL OR TEI.exhibition_no = ANY(in_exhibition_nos))
          AND (in_exhibition_item_nos IS NULL OR TEI.exhibition_item_no::text = ANY(in_exhibition_item_nos))
        GROUP BY TEI.exhibition_no
    ) TEI_MAX
      ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = in_member_no
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = in_lang_code
    WHERE TE.tenant_no = in_tenant_no
    AND (in_exhibition_nos IS NULL OR TE.exhibition_no = ANY(in_exhibition_nos))
      AND (in_exhibition_nos IS NOT NULL OR TE.status = 0) -- 入札開始から入札終了(延長含む)商品を表示する = 未確定のみ
      AND (in_auction_classification IS NULL OR TE.exhibition_classification_info->>'auctionClassification' = in_auction_classification)
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND (in_exhibition_nos IS NOT NULL OR now() < TEI_MAX.extended_end_datetime)

      AND (
            in_areas IS NULL
            OR array_length(in_areas, 1) IS NULL
            OR TE.category_id = ANY(in_areas)
          )
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
`

  const sql = ` WITH count_all_initial AS (
  -- Counting all by initial conditions
  SELECT COUNT(*) as count
    FROM (
      SELECT TEI.exhibition_item_no,
            array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
        FROM t_exhibition_item TEI
        JOIN filter_exhibition FE
          ON FE.exhibition_no = TEI.exhibition_no
        JOIN t_lot TL
          ON TL.lot_no = TEI.lot_no
        AND TL.tenant_no = in_tenant_no
        AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
        JOIN t_lot_detail TLD
          ON TLD.lot_no = TEI.lot_no
        AND TLD.tenant_no = in_tenant_no
        JOIN t_item TI
          ON TLD.item_no = TI.item_no
        AND TI.tenant_no = in_tenant_no
        AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
        JOIN t_item_localized TIL
          ON TIL.item_no = TI.item_no
        AND TIL.tenant_no = in_tenant_no
        AND TIL.language_code = in_lang_code
        AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
        AND (in_exhibition_nos IS NOT NULL OR TEI.hummer_flag = 0)
        AND (in_exhibition_nos IS NOT NULL OR now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime))
        AND TEI.exhibition_item_no IS NOT NULL
        GROUP BY TEI.exhibition_item_no
    ) CAT
),
`

  const sql = ` WITH filter_item AS (
  SELECT TEI.exhibition_no,
          TEI.exhibition_item_no,
          array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
  FROM t_exhibition_item TEI
    JOIN filter_exhibition FE
      ON FE.exhibition_no = TEI.exhibition_no
    JOIN t_lot TL
      ON TL.lot_no = TEI.lot_no
      AND TL.tenant_no = in_tenant_no
      AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
      AND TLD.tenant_no = in_tenant_no
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
      AND TI.tenant_no = in_tenant_no
      AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    JOIN t_item_localized TIL
      ON TIL.item_no = TI.item_no
      AND TIL.tenant_no = in_tenant_no
      AND TIL.language_code = in_lang_code
      AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      AND (in_brand IS NULL OR TIL.free_field->>'brand' = in_brand)
      AND (in_detail_item_no IS NULL OR NOT TIL.item_no = in_detail_item_no)
    LEFT JOIN t_exhibition_item_favorite TEIF
      ON TEIF.exhibition_item_no = TEI.exhibition_item_no
      AND TEIF.tenant_no = in_tenant_no
      AND TEIF.member_no = in_member_no
    LEFT JOIN t_bid TB
      ON TB.exhibition_item_no = TEI.exhibition_item_no
      AND TB.tenant_no = in_tenant_no
      AND TB.member_no = in_member_no
    -- RIGHT JOIN (
    --   SELECT in_category AS category_id
    -- ) CF
    --   ON TIL.free_field IS NOT NULL
    --   AND (CF.category_id IS NULL OR TIL.free_field->>'category' = CF.category_id::text)
    JOIN combined_categories CC
      ON CC.item_no = TI.item_no
  WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
    AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
    AND (in_favorite IS NULL
          OR (in_favorite IS TRUE AND TEIF.favorite_no IS NOT NULL)
          OR (in_favorite IS FALSE AND TEIF.favorite_no IS NULL))
    AND (in_start_price IS NULL OR COALESCE(TEI.current_price, TEI.lowest_bid_price) >= in_start_price::numeric)
    AND (in_end_price IS NULL OR in_end_price = 0 OR COALESCE(TEI.current_price, TEI.lowest_bid_price) <= in_end_price::numeric)
    AND (in_bidding IS NULL OR NOT in_bidding OR TB.bid_price IS NOT NULL)
    AND (in_exceeding_lowest_price IS NULL OR NOT in_exceeding_lowest_price OR TEI.top_member_no IS NOT NULL)
    AND (
        in_recommending IS NULL OR NOT in_recommending OR
        (TI.status = 2 AND now() BETWEEN TI.recommend_start_datetime AND TI.recommend_end_datetime)
    )
    AND (in_un_sold_out IS NULL OR NOT in_un_sold_out OR NOT (TEI.hummer_flag = 1))
    AND (in_exhibition_nos IS NOT NULL OR TEI.hummer_flag = 0)
    AND (in_exhibition_nos IS NOT NULL OR now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime))
    AND TEI.exhibition_item_no IS NOT NULL
    AND (
      in_search_keys IS NULL
      OR ARRAY_LENGTH(in_search_keys, 1) IS NULL
      OR (
        ((TIL.free_field->>'product_name')::VARCHAR ilike (SELECT CONCAT('%' || LOWER(EK.search_keys) || '%') from escapedKeywords EK))
        OR ((TIL.free_field->>'maker')::VARCHAR ilike (SELECT CONCAT('%' || LOWER(EK.search_keys) || '%') from escapedKeywords EK))
      )
    )
    AND (ARRAY_LENGTH(in_model_list, 1) IS NULL OR (
      SELECT COUNT(*)
      FROM unnest(in_model_list) AS keyword
      WHERE (((TIL.free_field->>'productName')::VARCHAR ilike CONCAT('%',keyword,'%')))
    ) > 0)
    AND(
      -- カテゴリー（in_categories）が選択されていない場合は全ての商品を返す
      ARRAY_LENGTH(in_categories, 1) IS NULL
      OR(  -- 全て選択されている場合は全ての商品を返す
          (SELECT COUNT(*) FROM unnest(in_categories) AS category) = (SELECT COUNT(value1) FROM productCategoryIncludeOther)
        )
      OR( -- "その他" と "その他" 以外のカテゴリーにチェックが入っている場合
        ARRAY_LENGTH(in_categories, 1) > 0
        AND TIL.free_field->>'category' =  ANY(in_categories)
        )
    )
    AND (ARRAY_LENGTH(in_brands, 1) IS NULL OR (
      TIL.free_field->>'brand' = ANY(in_brands)
    ))
  GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
),
`

  const sql = ` WITH count_all_items AS (
  SELECT count(*) FROM filter_item
),
`

  const sql = ` WITH category_group AS (
  SELECT jsonb_agg(json_build_object(
          'category_id', row.category_id::text,
          'count', row.count
        )) AS category_group
    FROM (
          SELECT
           unnest(FI.category_ids) AS category_id,
           count(*)
          FROM filter_item FI
          GROUP BY unnest(FI.category_ids)
        ) AS row
),
`

  const sql = ` WITH exhibition_group AS (
  SELECT jsonb_agg(json_build_object(
          'exhibition_no', FE.exhibition_no,
          'start_datetime', FE.start_datetime,
          'end_datetime', FE.end_datetime,
          'exhibition_name', FE.exhibition_name,
          'count' , FI.count,
          'auction_classification', FE.auction_classification
        )) AS category_group
    FROM filter_exhibition FE
    LEFT JOIN (
      SELECT
        FI.exhibition_no,
        count(*) as count
      FROM filter_item FI
      GROUP BY FI.exhibition_no
    ) FI
      ON FI.exhibition_no = FE.exhibition_no
),
`

  const sql = ` WITH count_lot_items AS (
  SELECT TLD.lot_no, count(*) as count
    FROM t_lot_detail TLD
    JOIN t_exhibition_item TEI ON TEI.lot_no = TLD.lot_no
    JOIN filter_exhibition FE ON FE.exhibition_no = TEI.exhibition_no
    WHERE TLD.tenant_no = in_tenant_no
    GROUP BY TLD.lot_no
),
`

  const sql = ` WITH bid_status AS ( --　Bid statusを取得する
 SELECT BST.exhibition_item_no,
      ((BST.result->'attention_info')->>'bid_count')::integer as bid_count,
      BST.result
  FROM f_get_auction_item_bid_status(
	  (SELECT array_agg(FI.exhibition_item_no) as exhibition_item_nos FROM filter_item FI),
    `

	  in_tenant_no,
	  in_member_no
  ) BST
),
`

  const sql = ` WITH items AS (
  SELECT  json_build_object(
            'item_no', TI.item_no,
            'manage_no', TI.manage_no,
            'exhibition_item_no', TEI.exhibition_item_no,
            'exhibition_no', TEI.exhibition_no,
            'auction_classification', FE.auction_classification,
            'category_id', FE.category_id,
            'lot_id', TL.lot_id,
            'item_count', CLI.count,
            'free_field', TIL.free_field,
            'image', (
                SELECT ARRAY[json_build_object('file_path', TIAF.file_path)] FROM t_item_ancillary_file TIAF
                WHERE TIAF.manage_no = TI.manage_no
                  AND TIAF.tenant_no = in_tenant_no
                  AND TIAF.item_no = TI.item_no
                  AND TIAF.division = 1
                  AND TIAF.delete_flag = 0
                  AND TIAF.postar_file_path IS NULL
                ORDER BY TIAF.serial_number
                LIMIT 1
              ),
              `

            'pdfs', (
                SELECT ARRAY[json_build_object('file_path', TIAF.file_path)] FROM t_item_ancillary_file TIAF
                WHERE TIAF.manage_no = TI.manage_no
                  AND TIAF.tenant_no = in_tenant_no
                  AND TIAF.item_no = TI.item_no
                  AND TIAF.division = 2
                  AND TIAF.delete_flag = 0
                LIMIT 1
              ),
              `

            'is_recommending', (TI.status = 2 AND now() BETWEEN TI.recommend_start_datetime AND TI.recommend_end_datetime),
            `

            'sold_out', TEI.hummer_flag = 1,
            'start_datetime', TEI.start_datetime,
            'end_datetime', FE.extended_end_datetime,
            'create_datetime', TEI.create_datetime
          )::jsonb || (
            COALESCE(STATUS.result, '{}'::jsonb)
          ) as item,
          -- 並べ替え用のrow_numberを取得 --
          ROW_NUMBER() OVER (
            PARTITION BY TEI.exhibition_no
            ORDER BY
              -- おすすめ順で並べ替える
              CASE
                  WHEN in_search_results_sort = 'recommend_sort' THEN
                      CASE
                          WHEN (TIL.free_field->>'hasRecommendation')::boolean IS TRUE THEN 1
                          ELSE 2
                      END
              END,
              CASE WHEN in_search_results_sort = 'recommend_sort' THEN TEI.create_datetime END DESC,

              -- 新着順で並べ替える
              CASE WHEN in_search_results_sort = 'create_datetime_desc' THEN TEI.create_datetime END DESC,

              -- 残り時間で並べ替える
              CASE WHEN in_search_results_sort = 'remain_time_asc' THEN (CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) END ASC,
              CASE WHEN in_search_results_sort = 'remain_time_desc' THEN (CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) END DESC,

              -- 現在の価格で並べ替える
              CASE WHEN in_search_results_sort = 'current_price_asc'
                  THEN COALESCE(TEI.current_price, TEI.lowest_bid_price)
              END ASC,
              CASE WHEN in_search_results_sort = 'current_price_desc'
                  THEN COALESCE(TEI.current_price, TEI.lowest_bid_price)
              END DESC,

              -- 入札数で並べ替える
              CASE WHEN in_search_results_sort = 'bid_count_asc' THEN COALESCE(STATUS.bid_count, 0) END ASC,
              CASE WHEN in_search_results_sort = 'bid_count_desc' THEN COALESCE(STATUS.bid_count, 0) END DESC,

            -- Lineup Items Sort
              CASE WHEN in_category_sort = 'asc' THEN COALESCE(MG.sort_order, 0) END ASC,
              CASE WHEN in_category_sort = 'desc' THEN COALESCE(MG.sort_order, 0) END DESC,
              CASE WHEN in_end_datetime_not_extend_sort = 'asc' THEN COALESCE(TEI.default_end_datetime, FE.end_datetime) END ASC,
              CASE WHEN in_end_datetime_not_extend_sort = 'desc' THEN COALESCE(TEI.default_end_datetime, FE.end_datetime) END DESC,

              -- Bid history Sort --
              CASE WHEN in_ended_datetime_sort = 'asc' THEN to_char(TEI.end_datetime, 'YYYY/MM/DD') END ASC,
              CASE WHEN in_ended_datetime_sort = 'desc' THEN to_char(TEI.end_datetime, 'YYYY/MM/DD') END DESC,
              CASE WHEN in_nickname_sort = 'asc' THEN (TIL.free_field->>'nickname') END ASC,
              CASE WHEN in_nickname_sort = 'desc' THEN (TIL.free_field->>'nickname') END DESC,

              -- Manage no sort --
              TI.manage_no ASC
          )
          AS row_number
    FROM t_exhibition_item TEI
    JOIN filter_exhibition FE
      ON FE.exhibition_no = TEI.exhibition_no
    LEFT JOIN bid_status STATUS
      ON STATUS.exhibition_item_no = TEI.exhibition_item_no
    JOIN t_lot TL
      ON TL.lot_no = TEI.lot_no
      AND TL.tenant_no = in_tenant_no
      AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
      AND TLD.tenant_no = in_tenant_no
      AND TLD.order_no = 1
    LEFT JOIN count_lot_items CLI
      ON CLI.lot_no = TEI.lot_no
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
      AND TI.tenant_no = in_tenant_no
      AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    JOIN t_item_localized TIL
      ON TIL.item_no = TI.item_no
      AND TIL.tenant_no = in_tenant_no
      AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      AND TIL.language_code = in_lang_code
      AND (in_brand IS NULL OR TIL.free_field->>'brand' = in_brand)
      AND (in_detail_item_no IS NULL OR NOT TIL.item_no = in_detail_item_no)
    JOIN filter_item FI
      ON TEI.exhibition_item_no = FI.exhibition_item_no
    LEFT OUTER JOIN (
          SELECT MCL.value1, MC.sort_order
            FROM m_constant MC
                  INNER JOIN m_constant_localized MCL
                          ON MC.constant_no = MCL.constant_no
                        AND MCL.language_code = in_lang_code
            WHERE MC.key_string = 'PRODUCT_CATEGORY' -- 定数キー変更
              AND (now() BETWEEN COALESCE(MC.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
              AND COALESCE(MC.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
      ) MG ON TIL.free_field->>'category' = MG.value1
    CROSS JOIN count_all_items
    WHERE (in_exhibition_nos IS NULL OR TEI.exhibition_no = ANY(in_exhibition_nos))
      AND (in_showed_item_nos IS NULL OR NOT TEI.exhibition_item_no::text = ANY(in_showed_item_nos))
      AND (in_exhibition_item_nos IS NULL OR TEI.exhibition_item_no::text = ANY(in_exhibition_item_nos))
),
`
const sql = ` WITH agg_items AS (
  SELECT jsonb_agg(IT.item) AS items
    FROM items IT
    WHERE IT.row_number <= in_limit
)

SELECT jsonb_build_object(
          'items', IT.items,
          'count', CAI.count,
          'count_init', CA_INIT.count,
          'category_group', CF.category_group,
          'exhibition_group', EG.category_group
        )
FROM agg_items IT
CROSS JOIN count_all_items CAI
CROSS JOIN category_group CF
CROSS JOIN exhibition_group EG
CROSS JOIN count_all_initial CA_INIT;
`




export const queryAnalyze = async pool => {
  const tenantId = 1
  const in_search_keys = ''
  const in_lang_code = 'ja'
  const category_other = '3'

  const escapedKeywords = await getEscapeKeywords(
    pool,
    tenantId,
    in_search_keys
  )
  console.log('🗑 log of escapedKeywords : ', escapedKeywords)

  const productCategoryExcludeOther = await productCategoryExcludeOther(
    pool,
    tenantId,
    in_lang_code,
    category_other
  )
}
