// Test script to demonstrate the progressive CTE analysis
// This shows how to use each stage independently

const { 
  queryAnalyze, 
  analyzeStage1, 
  analyzeStage2, 
  analyzeStage3, 
  analyzeStage4, 
  analyzeStage5, 
  analyzeStage6, 
  analyzeStage7,
  getCommonParams 
} = require('./query-analize')

// Example usage functions
const runFullAnalysis = async (pool) => {
  console.log('🚀 Running Full Progressive Analysis...')
  const result = await queryAnalyze(pool)
  return result
}

const runSingleStage = async (pool, stageNumber) => {
  console.log(`🔍 Running Single Stage ${stageNumber}...`)
  
  switch(stageNumber) {
    case 1:
      return await analyzeStage1(pool)
    case 2:
      return await analyzeStage2(pool)
    case 3:
      return await analyzeStage3(pool)
    case 4:
      return await analyzeStage4(pool)
    case 5:
      return await analyzeStage5(pool)
    case 6:
      return await analyzeStage6(pool)
    case 7:
      return await analyzeStage7(pool)
    default:
      throw new Error(`Invalid stage number: ${stageNumber}`)
  }
}

const runUpToStage = async (pool, maxStage) => {
  console.log(`🎯 Running Analysis up to Stage ${maxStage}...`)
  
  const results = []
  
  for (let i = 1; i <= maxStage; i++) {
    console.log(`\n--- Running Stage ${i} ---`)
    const result = await runSingleStage(pool, i)
    results.push(result)
    
    if (!result.success) {
      console.log(`❌ Stage ${i} failed, stopping analysis`)
      break
    }
  }
  
  return results
}

const debugSpecificIssue = async (pool) => {
  console.log('🔧 Debug Mode: Focusing on Critical Stages...')
  
  // Run only the critical stages that affect item filtering
  console.log('\n1. Checking Exhibition Filtering (Stage 5)...')
  const stage5 = await analyzeStage5(pool)
  
  if (!stage5.success || stage5.results.filter_exhibition.row_count === 0) {
    console.log('🚨 ISSUE: No exhibitions found! This is the root cause.')
    return { issue: 'no_exhibitions', stage: 5, result: stage5 }
  }
  
  console.log('\n2. Checking Initial Item Count (Stage 6)...')
  const stage6 = await analyzeStage6(pool)
  
  const initialCount = stage6.results.count_all_initial.results[0]?.count || 0
  if (initialCount === 0) {
    console.log('🚨 ISSUE: No items found in initial count! Check exhibition-item relationships.')
    return { issue: 'no_initial_items', stage: 6, result: stage6 }
  }
  
  console.log('\n3. Checking Final Item Filtering (Stage 7)...')
  const stage7 = await analyzeStage7(pool)
  
  const finalCount = stage7.results.filter_item.row_count
  if (finalCount === 0 && initialCount > 0) {
    console.log('🚨 ISSUE FOUND: Items are being filtered out in filter_item CTE!')
    console.log(`  - Initial: ${initialCount} items`)
    console.log(`  - Final: ${finalCount} items`)
    console.log('  - Check the WHERE conditions in filter_item')
    return { issue: 'items_filtered_out', stage: 7, result: stage7, initial_count: initialCount }
  }
  
  console.log('✅ No obvious issues found in critical stages')
  return { issue: 'none', stages: [stage5, stage6, stage7] }
}

// Export for use in other files
module.exports = {
  runFullAnalysis,
  runSingleStage,
  runUpToStage,
  debugSpecificIssue,
  getCommonParams
}

// Example usage (uncomment to test):
/*
const testAnalysis = async () => {
  // You would need to provide your pool instance here
  // const pool = yourPoolInstance
  
  // Run full analysis
  // const fullResult = await runFullAnalysis(pool)
  
  // Run only up to stage 5 (exhibition filtering)
  // const partialResult = await runUpToStage(pool, 5)
  
  // Run debug mode to find the specific issue
  // const debugResult = await debugSpecificIssue(pool)
  
  // Run a single stage
  // const singleResult = await runSingleStage(pool, 7)
}
*/
