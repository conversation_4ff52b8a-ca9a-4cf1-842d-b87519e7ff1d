-- Breakdown of f_search_auction_items function for pgAdmin4 execution - Part 2
-- Continuation from breakdown_queries.sql

-- Set tenant context (if using RLS)
SET app.current_tenant = '1';

-- Query 9: Category grouping
WITH filter_exhibition AS (
  SELECT TE.exhibition_no,
          TE.category_id,
          TE.pitch_width,
          TE.more_little_judge_pitch,
          TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
          TE.start_datetime,
          TE.end_datetime,
          TE.preview_end_datetime,
          TEI_MAX.extended_end_datetime,
          TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag=0
          AND TEI.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
        GROUP BY TEI.exhibition_no
    ) TEI_MAX ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
    AND TE.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
      AND TE.exhibition_classification_info->>'auctionClassification' = '2'
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND now() < TEI_MAX.extended_end_datetime
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
combined_categories AS (
  SELECT item_no, free_field->>'category' AS category_code
  FROM t_item_localized
  WHERE tenant_no = 1
    AND language_code = 'en'
    AND (delete_flag IS NULL OR delete_flag = 0)
),
filter_item AS (
  SELECT TEI.exhibition_no,
          TEI.exhibition_item_no,
          array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
  FROM t_exhibition_item TEI
    JOIN filter_exhibition FE ON FE.exhibition_no = TEI.exhibition_no
    JOIN t_lot TL
      ON TL.lot_no = TEI.lot_no
      AND TL.tenant_no = 1
      AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
      AND TLD.tenant_no = 1
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
      AND TI.tenant_no = 1
      AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    JOIN t_item_localized TIL
      ON TIL.item_no = TI.item_no
      AND TIL.tenant_no = 1
      AND TIL.language_code = 'en'
      AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
    LEFT JOIN t_exhibition_item_favorite TEIF
      ON TEIF.exhibition_item_no = TEI.exhibition_item_no
      AND TEIF.tenant_no = 1
      AND TEIF.member_no = 10
    LEFT JOIN t_bid TB
      ON TB.exhibition_item_no = TEI.exhibition_item_no
      AND TB.tenant_no = 1
      AND TB.member_no = 10
    JOIN combined_categories CC ON CC.item_no = TI.item_no
  WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
    AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
    AND TEI.hummer_flag = 0
    AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
    AND TEI.exhibition_item_no IS NOT NULL
  GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
),
category_group AS (
  SELECT jsonb_agg(json_build_object(
          'category_id', row.category_id::text,
          'count', row.count
        )) AS category_group
    FROM (
          SELECT
           unnest(FI.category_ids) AS category_id,
           count(*)
          FROM filter_item FI
          GROUP BY unnest(FI.category_ids)
        ) AS row
)
SELECT * FROM category_group;

-- Query 10: Exhibition grouping
WITH filter_exhibition AS (
  -- Same filter_exhibition CTE as above
  SELECT TE.exhibition_no,
          TE.category_id,
          TE.pitch_width,
          TE.more_little_judge_pitch,
          TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
          TE.start_datetime,
          TE.end_datetime,
          TE.preview_end_datetime,
          TEI_MAX.extended_end_datetime,
          TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag=0
          AND TEI.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
        GROUP BY TEI.exhibition_no
    ) TEI_MAX ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
    AND TE.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
      AND TE.exhibition_classification_info->>'auctionClassification' = '2'
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND now() < TEI_MAX.extended_end_datetime
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
combined_categories AS (
  SELECT item_no, free_field->>'category' AS category_code
  FROM t_item_localized
  WHERE tenant_no = 1
    AND language_code = 'en'
    AND (delete_flag IS NULL OR delete_flag = 0)
),
filter_item AS (
  SELECT TEI.exhibition_no,
          TEI.exhibition_item_no,
          array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
  FROM t_exhibition_item TEI
    JOIN filter_exhibition FE ON FE.exhibition_no = TEI.exhibition_no
    JOIN t_lot TL
      ON TL.lot_no = TEI.lot_no
      AND TL.tenant_no = 1
      AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
      AND TLD.tenant_no = 1
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
      AND TI.tenant_no = 1
      AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    JOIN t_item_localized TIL
      ON TIL.item_no = TI.item_no
      AND TIL.tenant_no = 1
      AND TIL.language_code = 'en'
      AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
    LEFT JOIN t_exhibition_item_favorite TEIF
      ON TEIF.exhibition_item_no = TEI.exhibition_item_no
      AND TEIF.tenant_no = 1
      AND TEIF.member_no = 10
    LEFT JOIN t_bid TB
      ON TB.exhibition_item_no = TEI.exhibition_item_no
      AND TB.tenant_no = 1
      AND TB.member_no = 10
    JOIN combined_categories CC ON CC.item_no = TI.item_no
  WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
    AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
    AND TEI.hummer_flag = 0
    AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
    AND TEI.exhibition_item_no IS NOT NULL
  GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
),
exhibition_group AS (
  SELECT jsonb_agg(json_build_object(
          'exhibition_no', FE.exhibition_no,
          'start_datetime', FE.start_datetime,
          'end_datetime', FE.end_datetime,
          'exhibition_name', FE.exhibition_name,
          'count' , FI.count,
          'auction_classification', FE.auction_classification
        )) AS category_group
    FROM filter_exhibition FE
    LEFT JOIN (
      SELECT
        FI.exhibition_no,
        count(*) as count
      FROM filter_item FI
      GROUP BY FI.exhibition_no
    ) FI ON FI.exhibition_no = FE.exhibition_no
)
SELECT * FROM exhibition_group;
