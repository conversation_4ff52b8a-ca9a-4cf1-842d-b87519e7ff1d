-- Breakdown of f_search_auction_items function for pgAdmin4 execution
-- Parameters used:
-- in_tenant_no = 1
-- in_exhibition_nos = [4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332]
-- in_auction_classification = '2'
-- in_lang_code = 'en'
-- in_member_no = 10 (converted from '10')
-- Other parameters are null/undefined

-- Set tenant context (if using RLS)
SET app.current_tenant = '1';

-- Query 1: Get escaped keywords (empty since in_search_keys is null)
WITH escapedKeywords AS (
  SELECT f_escape_string(keyword) AS search_keys
  FROM unnest(ARRAY[]::varchar[]) AS keyword
)
SELECT * FROM escapedKeywords;

-- Query 2: Get product categories excluding "other" (category 3)
WITH productCategoryExcludeOther AS(
  SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(value3,',')) as value3
  FROM m_constant mc
    INNER JOIN m_constant_localized mcl ON mcl.constant_no = mc.constant_no
  WHERE mc.tenant_no = 1
    AND mcl.language_code = 'en'
    AND mc.key_string = 'PRODUCT_CATEGORY'
    AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
    AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
    AND mcl.value1 <> '3'
)
SELECT * FROM productCategoryExcludeOther;

-- Query 3: Get product categories including "other"
WITH productCategoryIncludeOther AS (
  SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(value3,',')) as value3
  FROM m_constant mc
    INNER JOIN m_constant_localized mcl ON mcl.constant_no = mc.constant_no
  WHERE mc.tenant_no = 1
    AND mcl.language_code = 'en'
    AND mc.key_string = 'PRODUCT_CATEGORY'
    AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
    AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
)
SELECT * FROM productCategoryIncludeOther;

-- Query 4: Get combined categories
WITH combined_categories AS (
  SELECT item_no, free_field->>'category' AS category_code
  FROM t_item_localized
  WHERE tenant_no = 1
    AND language_code = 'en'
    AND (delete_flag IS NULL OR delete_flag = 0)
)
SELECT * FROM combined_categories LIMIT 100;

-- Query 5: Filter exhibitions based on criteria
WITH filter_exhibition AS (
  SELECT TE.exhibition_no,
          TE.category_id,
          TE.pitch_width,
          TE.more_little_judge_pitch,
          TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
          TE.start_datetime,
          TE.end_datetime,
          TE.preview_end_datetime,
          TEI_MAX.extended_end_datetime,
          TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag=0
          AND TEI.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
        GROUP BY TEI.exhibition_no
    ) TEI_MAX ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
    AND TE.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
      AND TE.exhibition_classification_info->>'auctionClassification' = '2'
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND now() < TEI_MAX.extended_end_datetime
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
)
SELECT * FROM filter_exhibition;

-- Query 6: Count all items by initial conditions
WITH filter_exhibition AS (
  -- Use the same filter_exhibition CTE from Query 5
  SELECT TE.exhibition_no,
          TE.category_id,
          TE.pitch_width,
          TE.more_little_judge_pitch,
          TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
          TE.start_datetime,
          TE.end_datetime,
          TE.preview_end_datetime,
          TEI_MAX.extended_end_datetime,
          TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag=0
          AND TEI.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
        GROUP BY TEI.exhibition_no
    ) TEI_MAX ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
    AND TE.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
      AND TE.exhibition_classification_info->>'auctionClassification' = '2'
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND now() < TEI_MAX.extended_end_datetime
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
count_all_initial AS (
  SELECT COUNT(*) as count
    FROM (
      SELECT TEI.exhibition_item_no,
            array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
        FROM t_exhibition_item TEI
        JOIN filter_exhibition FE ON FE.exhibition_no = TEI.exhibition_no
        JOIN t_lot TL
          ON TL.lot_no = TEI.lot_no
        AND TL.tenant_no = 1
        AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
        JOIN t_lot_detail TLD
          ON TLD.lot_no = TEI.lot_no
        AND TLD.tenant_no = 1
        JOIN t_item TI
          ON TLD.item_no = TI.item_no
        AND TI.tenant_no = 1
        AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
        JOIN t_item_localized TIL
          ON TIL.item_no = TI.item_no
        AND TIL.tenant_no = 1
        AND TIL.language_code = 'en'
        AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
        AND TEI.hummer_flag = 0
        AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
        AND TEI.exhibition_item_no IS NOT NULL
        GROUP BY TEI.exhibition_item_no
    ) CAT
)
SELECT * FROM count_all_initial;

-- Query 7: Filter items with all conditions
WITH filter_exhibition AS (
  -- Same as previous queries
  SELECT TE.exhibition_no,
          TE.category_id,
          TE.pitch_width,
          TE.more_little_judge_pitch,
          TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
          TE.start_datetime,
          TE.end_datetime,
          TE.preview_end_datetime,
          TEI_MAX.extended_end_datetime,
          TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag=0
          AND TEI.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
        GROUP BY TEI.exhibition_no
    ) TEI_MAX ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
    AND TE.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
      AND TE.exhibition_classification_info->>'auctionClassification' = '2'
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND now() < TEI_MAX.extended_end_datetime
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
combined_categories AS (
  SELECT item_no, free_field->>'category' AS category_code
  FROM t_item_localized
  WHERE tenant_no = 1
    AND language_code = 'en'
    AND (delete_flag IS NULL OR delete_flag = 0)
),
filter_item AS (
  SELECT TEI.exhibition_no,
          TEI.exhibition_item_no,
          array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
  FROM t_exhibition_item TEI
    JOIN filter_exhibition FE ON FE.exhibition_no = TEI.exhibition_no
    JOIN t_lot TL
      ON TL.lot_no = TEI.lot_no
      AND TL.tenant_no = 1
      AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
      AND TLD.tenant_no = 1
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
      AND TI.tenant_no = 1
      AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    JOIN t_item_localized TIL
      ON TIL.item_no = TI.item_no
      AND TIL.tenant_no = 1
      AND TIL.language_code = 'en'
      AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
    LEFT JOIN t_exhibition_item_favorite TEIF
      ON TEIF.exhibition_item_no = TEI.exhibition_item_no
      AND TEIF.tenant_no = 1
      AND TEIF.member_no = 10
    LEFT JOIN t_bid TB
      ON TB.exhibition_item_no = TEI.exhibition_item_no
      AND TB.tenant_no = 1
      AND TB.member_no = 10
    JOIN combined_categories CC ON CC.item_no = TI.item_no
  WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
    AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
    AND TEI.hummer_flag = 0
    AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
    AND TEI.exhibition_item_no IS NOT NULL
  GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
)
SELECT * FROM filter_item LIMIT 100;

-- Query 8: Count filtered items
WITH filter_exhibition AS (
  SELECT TE.exhibition_no,
          TE.category_id,
          TE.pitch_width,
          TE.more_little_judge_pitch,
          TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
          TE.start_datetime,
          TE.end_datetime,
          TE.preview_end_datetime,
          TEI_MAX.extended_end_datetime,
          TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag=0
          AND TEI.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
        GROUP BY TEI.exhibition_no
    ) TEI_MAX ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
    AND TE.exhibition_no = ANY(ARRAY[4,5,67,100,34,133,200,166,201,134,232,199,265,298,202,299,203,331,332])
      AND TE.exhibition_classification_info->>'auctionClassification' = '2'
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND now() < TEI_MAX.extended_end_datetime
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
combined_categories AS (
  SELECT item_no, free_field->>'category' AS category_code
  FROM t_item_localized
  WHERE tenant_no = 1
    AND language_code = 'en'
    AND (delete_flag IS NULL OR delete_flag = 0)
),
filter_item AS (
  SELECT TEI.exhibition_no,
          TEI.exhibition_item_no,
          array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
  FROM t_exhibition_item TEI
    JOIN filter_exhibition FE ON FE.exhibition_no = TEI.exhibition_no
    JOIN t_lot TL
      ON TL.lot_no = TEI.lot_no
      AND TL.tenant_no = 1
      AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
      AND TLD.tenant_no = 1
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
      AND TI.tenant_no = 1
      AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    JOIN t_item_localized TIL
      ON TIL.item_no = TI.item_no
      AND TIL.tenant_no = 1
      AND TIL.language_code = 'en'
      AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
    LEFT JOIN t_exhibition_item_favorite TEIF
      ON TEIF.exhibition_item_no = TEI.exhibition_item_no
      AND TEIF.tenant_no = 1
      AND TEIF.member_no = 10
    LEFT JOIN t_bid TB
      ON TB.exhibition_item_no = TEI.exhibition_item_no
      AND TB.tenant_no = 1
      AND TB.member_no = 10
    JOIN combined_categories CC ON CC.item_no = TI.item_no
  WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
    AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
    AND TEI.hummer_flag = 0
    AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
    AND TEI.exhibition_item_no IS NOT NULL
  GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
),
count_all_items AS (
  SELECT count(*) FROM filter_item
)
SELECT * FROM count_all_items;
