-- Debug Query 8: Final Items Query with Sorting
-- This query extracts the main items CTE with sorting logic from the original function
-- This is the core query that assembles all the item data

-- 8.1: Basic items query without complex sorting (for debugging)
WITH filter_exhibition AS (
    SELECT TE.exhibition_no,
           TE.category_id,
           TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
           TE.start_datetime,
           TE.end_datetime,
           TE.preview_end_datetime,
           TEI_MAX.extended_end_datetime,
           TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag = 0
        GROUP BY TEI.exhibition_no
    ) TEI_MAX
      ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
productCategoryIncludeOther AS (
    SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(value3,',')) as value3
    FROM m_constant mc
      INNER JOIN m_constant_localized mcl
          ON mcl.constant_no = mc.constant_no
    WHERE mc.tenant_no = 1
      AND mcl.language_code = 'en'
      AND mc.key_string = 'PRODUCT_CATEGORY'
      AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
),
combined_categories AS (
    SELECT item_no, free_field->>'category' AS category_code
    FROM t_item_localized
    WHERE tenant_no = 1
      AND language_code = 'en'
      AND (delete_flag IS NULL OR delete_flag = 0)
),
filter_item AS (
    SELECT TEI.exhibition_no,
            TEI.exhibition_item_no,
            array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
    FROM t_exhibition_item TEI
      JOIN filter_exhibition FE
        ON FE.exhibition_no = TEI.exhibition_no
      JOIN t_lot TL
        ON TL.lot_no = TEI.lot_no
        AND TL.tenant_no = 1
        AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
      JOIN t_lot_detail TLD
        ON TLD.lot_no = TEI.lot_no
        AND TLD.tenant_no = 1
      JOIN t_item TI
        ON TLD.item_no = TI.item_no
        AND TI.tenant_no = 1
        AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
      JOIN t_item_localized TIL
        ON TIL.item_no = TI.item_no
        AND TIL.tenant_no = 1
        AND TIL.language_code = 'en'
        AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      LEFT JOIN t_exhibition_item_favorite TEIF
        ON TEIF.exhibition_item_no = TEI.exhibition_item_no
        AND TEIF.tenant_no = 1
        AND TEIF.member_no = 10
      LEFT JOIN t_bid TB
        ON TB.exhibition_item_no = TEI.exhibition_item_no
        AND TB.tenant_no = 1
        AND TB.member_no = 10
      JOIN combined_categories CC
        ON CC.item_no = TI.item_no
    WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
      AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
      AND TEI.hummer_flag = 0
      AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
      AND TEI.exhibition_item_no IS NOT NULL
      AND (
        ARRAY_LENGTH(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'], 1) IS NULL
        OR (
            (SELECT COUNT(*) FROM unnest(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332']) AS category) = (SELECT COUNT(value1) FROM productCategoryIncludeOther)
          )
        OR (
          ARRAY_LENGTH(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'], 1) > 0
          AND TIL.free_field->>'category' = ANY(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'])
          )
      )
    GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
),
count_lot_items AS (
    SELECT TLD.lot_no, count(*) as count
    FROM t_lot_detail TLD
    JOIN t_exhibition_item TEI ON TEI.lot_no = TLD.lot_no
    JOIN filter_exhibition FE ON FE.exhibition_no = TEI.exhibition_no
    WHERE TLD.tenant_no = 1
    GROUP BY TLD.lot_no
)
SELECT 'items_basic' as query_name,
       TI.item_no,
       TI.manage_no,
       TEI.exhibition_item_no,
       TEI.exhibition_no,
       FE.auction_classification,
       FE.category_id,
       TL.lot_id,
       CLI.count as item_count,
       TIL.free_field->>'category' as category,
       TIL.free_field->>'product_name' as product_name,
       TIL.free_field->>'brand' as brand,
       TIL.free_field->>'maker' as maker,
       (TI.status = 2 AND now() BETWEEN TI.recommend_start_datetime AND TI.recommend_end_datetime) as is_recommending,
       TEI.hummer_flag = 1 as sold_out,
       TEI.start_datetime,
       TEI.current_price,
       TEI.lowest_bid_price,
       FE.extended_end_datetime as end_datetime,
       TEI.create_datetime
FROM t_exhibition_item TEI
JOIN filter_exhibition FE
  ON FE.exhibition_no = TEI.exhibition_no
JOIN t_lot TL
  ON TL.lot_no = TEI.lot_no
  AND TL.tenant_no = 1
  AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
JOIN t_lot_detail TLD
  ON TLD.lot_no = TEI.lot_no
  AND TLD.tenant_no = 1
  AND TLD.order_no = 1
LEFT JOIN count_lot_items CLI
  ON CLI.lot_no = TEI.lot_no
JOIN t_item TI
  ON TLD.item_no = TI.item_no
  AND TI.tenant_no = 1
  AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
JOIN t_item_localized TIL
  ON TIL.item_no = TI.item_no
  AND TIL.tenant_no = 1
  AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
  AND TIL.language_code = 'en'
JOIN filter_item FI
  ON TEI.exhibition_item_no = FI.exhibition_item_no
ORDER BY TI.manage_no ASC  -- Basic sorting by manage_no
LIMIT 20;  -- Limit for debugging

-- 8.2: Sample of item images and PDFs
SELECT 'item_files' as query_name,
       TI.item_no,
       TI.manage_no,
       TIAF.division,
       CASE WHEN TIAF.division = 1 THEN 'image' ELSE 'pdf' END as file_type,
       TIAF.file_path,
       TIAF.serial_number
FROM t_item TI
JOIN t_item_ancillary_file TIAF
  ON TIAF.manage_no = TI.manage_no
  AND TIAF.tenant_no = 1
  AND TIAF.item_no = TI.item_no
  AND TIAF.division IN (1, 2)  -- 1 = image, 2 = pdf
  AND TIAF.delete_flag = 0
  AND TIAF.postar_file_path IS NULL
WHERE TI.tenant_no = 1
  AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
ORDER BY TI.item_no, TIAF.division, TIAF.serial_number
LIMIT 20;  -- Sample for debugging

-- 8.3: Check product category sorting
SELECT 'category_sort_check' as query_name,
       MCL.value1 as category_code,
       MCL.value2 as category_name,
       MC.sort_order
FROM m_constant MC
INNER JOIN m_constant_localized MCL
    ON MC.constant_no = MCL.constant_no
    AND MCL.language_code = 'en'
WHERE MC.key_string = 'PRODUCT_CATEGORY'
  AND (now() BETWEEN COALESCE(MC.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
  AND COALESCE(MC.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
ORDER BY MC.sort_order, MCL.value1;
