-- Debug Query 7: Bid Status and Lot Items
-- This query extracts the bid_status and count_lot_items CTEs from the original function
-- Note: The bid status function f_get_auction_item_bid_status needs to be handled separately

-- 7.1: Count lot items
WITH filter_exhibition AS (
    SELECT TE.exhibition_no,
           TE.category_id,
           TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
           TE.start_datetime,
           TE.end_datetime,
           TE.preview_end_datetime,
           TEI_MAX.extended_end_datetime,
           TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag = 0
        GROUP BY TEI.exhibition_no
    ) TEI_MAX
      ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
)
SELECT 'count_lot_items' as query_name,
       TLD.lot_no,
       count(*) as item_count_per_lot
FROM t_lot_detail TLD
JOIN t_exhibition_item TEI ON TEI.lot_no = TLD.lot_no
JOIN filter_exhibition FE ON FE.exhibition_no = TEI.exhibition_no
WHERE TLD.tenant_no = 1
GROUP BY TLD.lot_no
ORDER BY item_count_per_lot DESC
LIMIT 20;  -- Limit for debugging

-- 7.2: Sample bid information (since we can't easily call the bid status function)
-- This shows what bid-related data is available in the tables
SELECT 'bid_sample_data' as query_name,
       TB.exhibition_item_no,
       TB.member_no,
       TB.bid_price,
       TB.bid_datetime,
       TB.bid_no,
       TEI.current_price,
       TEI.lowest_bid_price,
       TEI.top_member_no
FROM t_bid TB
JOIN t_exhibition_item TEI ON TEI.exhibition_item_no = TB.exhibition_item_no
WHERE TB.tenant_no = 1
  AND TB.member_no = 10  -- in_member_no
ORDER BY TB.bid_datetime DESC
LIMIT 10;  -- Sample for debugging

-- 7.3: Exhibition items with bid information
SELECT 'exhibition_item_bid_info' as query_name,
       TEI.exhibition_item_no,
       TEI.exhibition_no,
       TEI.current_price,
       TEI.lowest_bid_price,
       TEI.top_member_no,
       TEI.hummer_flag,
       COUNT(TB.bid_no) as bid_count,
       MAX(TB.bid_price) as highest_bid,
       MIN(TB.bid_price) as lowest_bid
FROM t_exhibition_item TEI
LEFT JOIN t_bid TB ON TB.exhibition_item_no = TEI.exhibition_item_no
                  AND TB.tenant_no = 1
WHERE TEI.tenant_no = 1
  AND (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
GROUP BY TEI.exhibition_item_no, TEI.exhibition_no, TEI.current_price, TEI.lowest_bid_price, TEI.top_member_no, TEI.hummer_flag
ORDER BY bid_count DESC, TEI.exhibition_item_no
LIMIT 20;  -- Sample for debugging

-- 7.4: Check if bid status function exists and sample call
-- Note: This is a placeholder for the f_get_auction_item_bid_status function
-- You may need to run this separately or check if the function exists in your database

-- Uncomment and modify the following if you want to test the bid status function:
/*
SELECT 'bid_status_function_test' as query_name,
       BST.exhibition_item_no,
       ((BST.result->'attention_info')->>'bid_count')::integer as bid_count,
       BST.result
FROM f_get_auction_item_bid_status(
    ARRAY[1001, 1002, 1003], -- Sample exhibition_item_nos
    1,    -- in_tenant_no
    10    -- in_member_no
) BST
LIMIT 5;
*/

-- 7.5: Check for functions that might be related to bid status
SELECT 'available_functions' as query_name,
       proname as function_name,
       pronargs as num_args
FROM pg_proc
WHERE proname LIKE '%bid%' OR proname LIKE '%auction%'
ORDER BY proname;
