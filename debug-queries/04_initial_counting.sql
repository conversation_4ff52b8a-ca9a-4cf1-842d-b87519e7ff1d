-- Debug Query 4: Initial Item Counting
-- This query extracts the count_all_initial CTE from the original function
-- It counts all items by initial conditions before applying detailed filters

-- 4.1: Count all items by initial conditions
-- This is a simplified version of the count_all_initial CTE
WITH filter_exhibition AS (
    SELECT TE.exhibition_no,
           TE.category_id,
           TE.pitch_width,
           TE.more_little_judge_pitch,
           TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
           TE.start_datetime,
           TE.end_datetime,
           TE.preview_end_datetime,
           TEI_MAX.extended_end_datetime,
           TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag = 0
        GROUP BY TEI.exhibition_no
    ) TEI_MAX
      ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10  -- in_member_no
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
)
SELECT 'count_all_initial' as query_name,
       COUNT(*) as total_count,
       COUNT(DISTINCT TEI.exhibition_item_no) as unique_exhibition_items,
       COUNT(DISTINCT TI.item_no) as unique_items
FROM t_exhibition_item TEI
JOIN filter_exhibition FE
  ON FE.exhibition_no = TEI.exhibition_no
JOIN t_lot TL
  ON TL.lot_no = TEI.lot_no
  AND TL.tenant_no = 1
  AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
JOIN t_lot_detail TLD
  ON TLD.lot_no = TEI.lot_no
  AND TLD.tenant_no = 1
JOIN t_item TI
  ON TLD.item_no = TI.item_no
  AND TI.tenant_no = 1
  AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
JOIN t_item_localized TIL
  ON TIL.item_no = TI.item_no
  AND TIL.tenant_no = 1
  AND TIL.language_code = 'en'
  AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
  AND TEI.hummer_flag = 0
  AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
  AND TEI.exhibition_item_no IS NOT NULL;

-- 4.2: Sample of items found in initial count
WITH filter_exhibition AS (
    SELECT TE.exhibition_no,
           TE.category_id,
           TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
           TE.start_datetime,
           TE.end_datetime,
           TE.preview_end_datetime,
           TEI_MAX.extended_end_datetime,
           TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag = 0
        GROUP BY TEI.exhibition_no
    ) TEI_MAX
      ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
)
SELECT 'sample_initial_items' as query_name,
       TEI.exhibition_item_no,
       TEI.exhibition_no,
       TI.item_no,
       TI.manage_no,
       TIL.free_field->>'category' AS category_code,
       TIL.free_field->>'product_name' AS product_name,
       TEI.hummer_flag,
       TEI.current_price,
       TEI.lowest_bid_price
FROM t_exhibition_item TEI
JOIN filter_exhibition FE
  ON FE.exhibition_no = TEI.exhibition_no
JOIN t_lot TL
  ON TL.lot_no = TEI.lot_no
  AND TL.tenant_no = 1
  AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
JOIN t_lot_detail TLD
  ON TLD.lot_no = TEI.lot_no
  AND TLD.tenant_no = 1
JOIN t_item TI
  ON TLD.item_no = TI.item_no
  AND TI.tenant_no = 1
  AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
JOIN t_item_localized TIL
  ON TIL.item_no = TI.item_no
  AND TIL.tenant_no = 1
  AND TIL.language_code = 'en'
  AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
  AND TEI.hummer_flag = 0
  AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
  AND TEI.exhibition_item_no IS NOT NULL
ORDER BY TEI.exhibition_item_no
LIMIT 10;  -- Sample for debugging
