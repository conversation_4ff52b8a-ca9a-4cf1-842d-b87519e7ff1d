-- Debug Query 1: Parameter Setup
-- This query sets up the parameters that will be used throughout the debugging process
-- Replace the stored function parameters with actual values for testing

-- Parameter values based on your test case
-- You can modify these values as needed for different test scenarios

-- Core parameters
DO $$
DECLARE
    in_tenant_no bigint := 1;
    in_lang_code character varying := 'en';
    in_member_no bigint := 10;
    in_categories character varying[] := ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'];

    -- Other parameters set to NULL for debugging (modify as needed)
    in_exhibition_nos bigint[] := NULL;
    in_auction_classification character varying := NULL;
    in_search_keys character varying[] := NULL;
    in_areas integer[] := NULL;
    in_category integer := NULL;
    in_start_year numeric := NULL;
    in_end_year numeric := NULL;
    in_start_price numeric := NULL;
    in_end_price numeric := NULL;
    in_favorite boolean := NULL;
    in_bidding boolean := NULL;
    in_un_sold_out boolean := NULL;
    in_recommending boolean := NULL;
    in_exceeding_lowest_price boolean := NULL;
    in_search_results_sort character varying := NULL;
    in_category_sort character varying := NULL;
    in_end_datetime_not_extend_sort character varying := NULL;
    in_ended_datetime_sort character varying := NULL;
    in_nickname_sort character varying := NULL;
    in_limit numeric := 50;
    in_showed_item_nos text[] := NULL;
    in_exhibition_item_nos text[] := NULL;
    in_model_list character varying[] := NULL;
    in_brand character varying := NULL;
    in_detail_item_no bigint := NULL;
    in_brands character varying[] := NULL;

    category_other text := '3';
BEGIN
    RAISE NOTICE 'Parameters set up for debugging:';
    RAISE NOTICE 'Tenant No: %', in_tenant_no;
    RAISE NOTICE 'Language: %', in_lang_code;
    RAISE NOTICE 'Member No: %', in_member_no;
    RAISE NOTICE 'Categories: %', in_categories;
    RAISE NOTICE 'Limit: %', in_limit;
END $$;

-- Verify parameter setup by displaying current timestamp and basic info
SELECT
    now() as current_timestamp,
    'Debug session started' as status,
    1 as tenant_no,
    'en' as language_code,
    10 as member_no,
    ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'] as categories_to_filter;
