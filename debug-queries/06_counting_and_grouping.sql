-- Debug Query 6: Counting and Grouping
-- This query extracts the counting and grouping CTEs from the original function
-- Includes count_all_items, category_group, and exhibition_group

-- 6.1: Final item count after all filters
WITH filter_exhibition AS (
    SELECT TE.exhibition_no,
           TE.category_id,
           TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
           TE.start_datetime,
           TE.end_datetime,
           TE.preview_end_datetime,
           TEI_MAX.extended_end_datetime,
           TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag = 0
        GROUP BY TEI.exhibition_no
    ) TEI_MAX
      ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
productCategoryIncludeOther AS (
    SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(value3,',')) as value3
    FROM m_constant mc
      INNER JOIN m_constant_localized mcl
          ON mcl.constant_no = mc.constant_no
    WHERE mc.tenant_no = 1
      AND mcl.language_code = 'en'
      AND mc.key_string = 'PRODUCT_CATEGORY'
      AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
),
combined_categories AS (
    SELECT item_no, free_field->>'category' AS category_code
    FROM t_item_localized
    WHERE tenant_no = 1
      AND language_code = 'en'
      AND (delete_flag IS NULL OR delete_flag = 0)
),
filter_item AS (
    SELECT TEI.exhibition_no,
            TEI.exhibition_item_no,
            array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
    FROM t_exhibition_item TEI
      JOIN filter_exhibition FE
        ON FE.exhibition_no = TEI.exhibition_no
      JOIN t_lot TL
        ON TL.lot_no = TEI.lot_no
        AND TL.tenant_no = 1
        AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
      JOIN t_lot_detail TLD
        ON TLD.lot_no = TEI.lot_no
        AND TLD.tenant_no = 1
      JOIN t_item TI
        ON TLD.item_no = TI.item_no
        AND TI.tenant_no = 1
        AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
      JOIN t_item_localized TIL
        ON TIL.item_no = TI.item_no
        AND TIL.tenant_no = 1
        AND TIL.language_code = 'en'
        AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      LEFT JOIN t_exhibition_item_favorite TEIF
        ON TEIF.exhibition_item_no = TEI.exhibition_item_no
        AND TEIF.tenant_no = 1
        AND TEIF.member_no = 10
      LEFT JOIN t_bid TB
        ON TB.exhibition_item_no = TEI.exhibition_item_no
        AND TB.tenant_no = 1
        AND TB.member_no = 10
      JOIN combined_categories CC
        ON CC.item_no = TI.item_no
    WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
      AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
      AND TEI.hummer_flag = 0
      AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
      AND TEI.exhibition_item_no IS NOT NULL
      AND (
        ARRAY_LENGTH(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'], 1) IS NULL
        OR (
            (SELECT COUNT(*) FROM unnest(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332']) AS category) = (SELECT COUNT(value1) FROM productCategoryIncludeOther)
          )
        OR (
          ARRAY_LENGTH(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'], 1) > 0
          AND TIL.free_field->>'category' = ANY(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'])
          )
      )
    GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
)
SELECT 'count_all_items' as query_name,
       COUNT(*) as final_item_count,
       'Items after all filters applied' as description
FROM filter_item;

-- 6.2: Category group aggregation
WITH filter_exhibition AS (
    SELECT TE.exhibition_no,
           TE.category_id,
           TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
           TE.start_datetime,
           TE.end_datetime,
           TE.preview_end_datetime,
           TEI_MAX.extended_end_datetime,
           TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag = 0
        GROUP BY TEI.exhibition_no
    ) TEI_MAX
      ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
productCategoryIncludeOther AS (
    SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(value3,',')) as value3
    FROM m_constant mc
      INNER JOIN m_constant_localized mcl
          ON mcl.constant_no = mc.constant_no
    WHERE mc.tenant_no = 1
      AND mcl.language_code = 'en'
      AND mc.key_string = 'PRODUCT_CATEGORY'
      AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
),
combined_categories AS (
    SELECT item_no, free_field->>'category' AS category_code
    FROM t_item_localized
    WHERE tenant_no = 1
      AND language_code = 'en'
      AND (delete_flag IS NULL OR delete_flag = 0)
),
filter_item AS (
    SELECT TEI.exhibition_no,
            TEI.exhibition_item_no,
            array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
    FROM t_exhibition_item TEI
      JOIN filter_exhibition FE
        ON FE.exhibition_no = TEI.exhibition_no
      JOIN t_lot TL
        ON TL.lot_no = TEI.lot_no
        AND TL.tenant_no = 1
        AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
      JOIN t_lot_detail TLD
        ON TLD.lot_no = TEI.lot_no
        AND TLD.tenant_no = 1
      JOIN t_item TI
        ON TLD.item_no = TI.item_no
        AND TI.tenant_no = 1
        AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
      JOIN t_item_localized TIL
        ON TIL.item_no = TI.item_no
        AND TIL.tenant_no = 1
        AND TIL.language_code = 'en'
        AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      LEFT JOIN t_exhibition_item_favorite TEIF
        ON TEIF.exhibition_item_no = TEI.exhibition_item_no
        AND TEIF.tenant_no = 1
        AND TEIF.member_no = 10
      LEFT JOIN t_bid TB
        ON TB.exhibition_item_no = TEI.exhibition_item_no
        AND TB.tenant_no = 1
        AND TB.member_no = 10
      JOIN combined_categories CC
        ON CC.item_no = TI.item_no
    WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
      AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
      AND TEI.hummer_flag = 0
      AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
      AND TEI.exhibition_item_no IS NOT NULL
      AND (
        ARRAY_LENGTH(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'], 1) IS NULL
        OR (
            (SELECT COUNT(*) FROM unnest(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332']) AS category) = (SELECT COUNT(value1) FROM productCategoryIncludeOther)
          )
        OR (
          ARRAY_LENGTH(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'], 1) > 0
          AND TIL.free_field->>'category' = ANY(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'])
          )
      )
    GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
)
SELECT 'category_group' as query_name,
       unnest(FI.category_ids) AS category_id,
       count(*) as category_count
FROM filter_item FI
GROUP BY unnest(FI.category_ids)
ORDER BY category_count DESC;

-- 6.3: Exhibition group aggregation
WITH filter_exhibition AS (
    SELECT TE.exhibition_no,
           TE.category_id,
           TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
           TE.start_datetime,
           TE.end_datetime,
           TE.preview_end_datetime,
           TEI_MAX.extended_end_datetime,
           TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag = 0
        GROUP BY TEI.exhibition_no
    ) TEI_MAX
      ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
productCategoryIncludeOther AS (
    SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(value3,',')) as value3
    FROM m_constant mc
      INNER JOIN m_constant_localized mcl
          ON mcl.constant_no = mc.constant_no
    WHERE mc.tenant_no = 1
      AND mcl.language_code = 'en'
      AND mc.key_string = 'PRODUCT_CATEGORY'
      AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
),
combined_categories AS (
    SELECT item_no, free_field->>'category' AS category_code
    FROM t_item_localized
    WHERE tenant_no = 1
      AND language_code = 'en'
      AND (delete_flag IS NULL OR delete_flag = 0)
),
filter_item AS (
    SELECT TEI.exhibition_no,
            TEI.exhibition_item_no,
            array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
    FROM t_exhibition_item TEI
      JOIN filter_exhibition FE
        ON FE.exhibition_no = TEI.exhibition_no
      JOIN t_lot TL
        ON TL.lot_no = TEI.lot_no
        AND TL.tenant_no = 1
        AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
      JOIN t_lot_detail TLD
        ON TLD.lot_no = TEI.lot_no
        AND TLD.tenant_no = 1
      JOIN t_item TI
        ON TLD.item_no = TI.item_no
        AND TI.tenant_no = 1
        AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
      JOIN t_item_localized TIL
        ON TIL.item_no = TI.item_no
        AND TIL.tenant_no = 1
        AND TIL.language_code = 'en'
        AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      LEFT JOIN t_exhibition_item_favorite TEIF
        ON TEIF.exhibition_item_no = TEI.exhibition_item_no
        AND TEIF.tenant_no = 1
        AND TEIF.member_no = 10
      LEFT JOIN t_bid TB
        ON TB.exhibition_item_no = TEI.exhibition_item_no
        AND TB.tenant_no = 1
        AND TB.member_no = 10
      JOIN combined_categories CC
        ON CC.item_no = TI.item_no
    WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
      AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
      AND TEI.hummer_flag = 0
      AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
      AND TEI.exhibition_item_no IS NOT NULL
      AND (
        ARRAY_LENGTH(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'], 1) IS NULL
        OR (
            (SELECT COUNT(*) FROM unnest(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332']) AS category) = (SELECT COUNT(value1) FROM productCategoryIncludeOther)
          )
        OR (
          ARRAY_LENGTH(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'], 1) > 0
          AND TIL.free_field->>'category' = ANY(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'])
          )
      )
    GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
)
SELECT 'exhibition_group' as query_name,
       FE.exhibition_no,
       FE.start_datetime,
       FE.end_datetime,
       FE.exhibition_name,
       FE.auction_classification,
       COALESCE(FI.count, 0) as item_count
FROM filter_exhibition FE
LEFT JOIN (
  SELECT
    FI.exhibition_no,
    count(*) as count
  FROM filter_item FI
  GROUP BY FI.exhibition_no
) FI
  ON FI.exhibition_no = FE.exhibition_no
ORDER BY FE.exhibition_no;
