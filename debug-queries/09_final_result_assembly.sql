-- Debug Query 9: Final Result Assembly and JSON Building
-- This query shows the final result structure as it would be returned by the function

-- 9.1: Final JSON structure assembly (simplified version)
WITH filter_exhibition AS (
    SELECT TE.exhibition_no,
           TE.category_id,
           TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
           TE.start_datetime,
           TE.end_datetime,
           TE.preview_end_datetime,
           TEI_MAX.extended_end_datetime,
           TEL.exhibition_name
    FROM t_exhibition TE
    JOIN (
        SELECT TEI.exhibition_no,
                MAX(CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                  END) as extended_end_datetime
          FROM t_exhibition_item TEI
        WHERE TEI.delete_flag = 0
        GROUP BY TEI.exhibition_no
    ) TEI_MAX
      ON TEI_MAX.exhibition_no = TE.exhibition_no
    LEFT JOIN t_exhibition_member TEM
      ON TEM.exhibition_no = TE.exhibition_no
      AND TEM.member_no = 10
    LEFT JOIN t_exhibition_localized TEL
      ON TEL.exhibition_no = TE.exhibition_no
      AND TEL.language_code = 'en'
    WHERE TE.tenant_no = 1
      AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
      AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
      AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
),
productCategoryIncludeOther AS (
    SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(value3,',')) as value3
    FROM m_constant mc
      INNER JOIN m_constant_localized mcl
          ON mcl.constant_no = mc.constant_no
    WHERE mc.tenant_no = 1
      AND mcl.language_code = 'en'
      AND mc.key_string = 'PRODUCT_CATEGORY'
      AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
),
combined_categories AS (
    SELECT item_no, free_field->>'category' AS category_code
    FROM t_item_localized
    WHERE tenant_no = 1
      AND language_code = 'en'
      AND (delete_flag IS NULL OR delete_flag = 0)
),
filter_item AS (
    SELECT TEI.exhibition_no,
            TEI.exhibition_item_no,
            array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
    FROM t_exhibition_item TEI
      JOIN filter_exhibition FE
        ON FE.exhibition_no = TEI.exhibition_no
      JOIN t_lot TL
        ON TL.lot_no = TEI.lot_no
        AND TL.tenant_no = 1
        AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
      JOIN t_lot_detail TLD
        ON TLD.lot_no = TEI.lot_no
        AND TLD.tenant_no = 1
      JOIN t_item TI
        ON TLD.item_no = TI.item_no
        AND TI.tenant_no = 1
        AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
      JOIN t_item_localized TIL
        ON TIL.item_no = TI.item_no
        AND TIL.tenant_no = 1
        AND TIL.language_code = 'en'
        AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      LEFT JOIN t_exhibition_item_favorite TEIF
        ON TEIF.exhibition_item_no = TEI.exhibition_item_no
        AND TEIF.tenant_no = 1
        AND TEIF.member_no = 10
      LEFT JOIN t_bid TB
        ON TB.exhibition_item_no = TEI.exhibition_item_no
        AND TB.tenant_no = 1
        AND TB.member_no = 10
      JOIN combined_categories CC
        ON CC.item_no = TI.item_no
    WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
      AND (TEI.cancel_flag IS NULL OR TEI.cancel_flag = 0)
      AND TEI.hummer_flag = 0
      AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
      AND TEI.exhibition_item_no IS NOT NULL
      AND (
        ARRAY_LENGTH(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'], 1) IS NULL
        OR (
            (SELECT COUNT(*) FROM unnest(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332']) AS category) = (SELECT COUNT(value1) FROM productCategoryIncludeOther)
          )
        OR (
          ARRAY_LENGTH(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'], 1) > 0
          AND TIL.free_field->>'category' = ANY(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'])
          )
      )
    GROUP BY TEI.exhibition_no, TEI.exhibition_item_no
),
count_all_items AS (
    SELECT count(*) as total_filtered_items FROM filter_item
),
count_all_initial AS (
    SELECT COUNT(*) as total_initial_items
      FROM (
        SELECT TEI.exhibition_item_no,
              array_agg(DISTINCT TIL.free_field->>'category') AS category_ids
          FROM t_exhibition_item TEI
          JOIN filter_exhibition FE
            ON FE.exhibition_no = TEI.exhibition_no
          JOIN t_lot TL
            ON TL.lot_no = TEI.lot_no
          AND TL.tenant_no = 1
          AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
          JOIN t_lot_detail TLD
            ON TLD.lot_no = TEI.lot_no
          AND TLD.tenant_no = 1
          JOIN t_item TI
            ON TLD.item_no = TI.item_no
          AND TI.tenant_no = 1
          AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
          JOIN t_item_localized TIL
            ON TIL.item_no = TI.item_no
          AND TIL.tenant_no = 1
          AND TIL.language_code = 'en'
          AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
        WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
          AND TEI.hummer_flag = 0
          AND now() < COALESCE(FE.preview_end_datetime, FE.extended_end_datetime)
          AND TEI.exhibition_item_no IS NOT NULL
          GROUP BY TEI.exhibition_item_no
      ) CAT
),
category_group AS (
    SELECT jsonb_agg(json_build_object(
            'category_id', row.category_id::text,
            'count', row.count
          )) AS category_group
      FROM (
            SELECT
             unnest(FI.category_ids) AS category_id,
             count(*)
            FROM filter_item FI
            GROUP BY unnest(FI.category_ids)
          ) AS row
),
exhibition_group AS (
    SELECT jsonb_agg(json_build_object(
            'exhibition_no', FE.exhibition_no,
            'start_datetime', FE.start_datetime,
            'end_datetime', FE.end_datetime,
            'exhibition_name', FE.exhibition_name,
            'count' , COALESCE(FI.count, 0),
            'auction_classification', FE.auction_classification
          )) AS exhibition_group
      FROM filter_exhibition FE
      LEFT JOIN (
        SELECT
          FI.exhibition_no,
          count(*) as count
        FROM filter_item FI
        GROUP BY FI.exhibition_no
      ) FI
        ON FI.exhibition_no = FE.exhibition_no
)
SELECT 'final_result_structure' as query_name,
       jsonb_build_object(
            'items', jsonb_build_array(),  -- Would contain the actual items
            'count', CAI.total_filtered_items,
            'count_init', CA_INIT.total_initial_items,
            'category_group', CG.category_group,
            'exhibition_group', EG.exhibition_group
          ) as result_json
FROM count_all_items CAI
CROSS JOIN category_group CG
CROSS JOIN exhibition_group EG
CROSS JOIN count_all_initial CA_INIT;

-- 9.2: Summary of debugging results
SELECT 'debug_summary' as query_name,
       'Step' as step_name,
       'Description' as description,
       'Status' as status

UNION ALL

SELECT 'debug_summary' as query_name,
       '1. Parameters' as step_name,
       'Set up test parameters' as description,
       'Ready' as status

UNION ALL

SELECT 'debug_summary' as query_name,
       '2. Categories' as step_name,
       'Query product categories' as description,
       'Ready' as status

UNION ALL

SELECT 'debug_summary' as query_name,
       '3. Exhibitions' as step_name,
       'Filter exhibitions' as description,
       'Ready' as status

UNION ALL

SELECT 'debug_summary' as query_name,
       '4. Initial Count' as step_name,
       'Count items before filtering' as description,
       'Ready' as status

UNION ALL

SELECT 'debug_summary' as query_name,
       '5. Item Filtering' as step_name,
       'Apply category and other filters' as description,
       'Ready' as status

UNION ALL

SELECT 'debug_summary' as query_name,
       '6. Grouping' as step_name,
       'Group by category and exhibition' as description,
       'Ready' as status

UNION ALL

SELECT 'debug_summary' as query_name,
       '7. Bid Status' as step_name,
       'Get bid information' as description,
       'Needs function check' as status

UNION ALL

SELECT 'debug_summary' as query_name,
       '8. Final Items' as step_name,
       'Assemble final item data' as description,
       'Ready' as status

UNION ALL

SELECT 'debug_summary' as query_name,
       '9. Result JSON' as step_name,
       'Build final JSON result' as description,
       'Ready' as status;

-- 9.3: Check current parameter values used in debugging
SELECT 'parameter_check' as query_name,
       'tenant_no' as parameter_name,
       '1' as value

UNION ALL

SELECT 'parameter_check' as query_name,
       'lang_code' as parameter_name,
       'en' as value

UNION ALL

SELECT 'parameter_check' as query_name,
       'member_no' as parameter_name,
       '10' as value

UNION ALL

SELECT 'parameter_check' as query_name,
       'categories' as parameter_name,
       array_to_string(ARRAY['4', '5', '67', '100', '34', '133', '200', '166', '201', '134', '232', '199', '265', '298', '202', '299', '203', '331', '332'], ',') as value

UNION ALL

SELECT 'parameter_check' as query_name,
       'limit' as parameter_name,
       '50' as value;
