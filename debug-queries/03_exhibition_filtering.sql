-- Debug Query 3: Exhibition Filtering
-- This query extracts the filter_exhibition CTE from the original function
-- This is a complex query that filters exhibitions based on various criteria

-- 3.1: Get extended end datetime for exhibitions
SELECT 'exhibition_extended_times' as query_name,
       TEI.exhibition_no,
       MAX(CASE
             WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
             ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
         END) as extended_end_datetime,
       COUNT(*) as item_count
FROM t_exhibition_item TEI
WHERE TEI.delete_flag = 0
  -- Add your specific exhibition filters here if needed
  -- AND (in_exhibition_nos IS NULL OR TEI.exhibition_no = ANY(in_exhibition_nos))
  -- AND (in_exhibition_item_nos IS NULL OR TEI.exhibition_item_no::text = ANY(in_exhibition_item_nos))
GROUP BY TEI.exhibition_no
ORDER BY TEI.exhibition_no
LIMIT 20;  -- Limit for debugging

-- 3.2: Main exhibition filtering query
SELECT 'filter_exhibition' as query_name,
       TE.exhibition_no,
       TE.category_id,
       TE.pitch_width,
       TE.more_little_judge_pitch,
       TE.exhibition_classification_info->>'auctionClassification' AS auction_classification,
       TE.start_datetime,
       TE.end_datetime,
       TE.preview_end_datetime,
       TEI_MAX.extended_end_datetime,
       TEL.exhibition_name,
       TE.status,
       TE.exhibition_classification_info->>'openClassification' as open_classification,
       CASE WHEN TEM.exhibition_member_no IS NOT NULL THEN 'member' ELSE 'not_member' END as membership_status
FROM t_exhibition TE
JOIN (
    SELECT TEI.exhibition_no,
            MAX(CASE
                  WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                  ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
              END) as extended_end_datetime
      FROM t_exhibition_item TEI
    WHERE TEI.delete_flag = 0
      -- Add your specific exhibition item filters here if needed
      -- AND (in_exhibition_nos IS NULL OR TEI.exhibition_no = ANY(in_exhibition_nos))
      -- AND (in_exhibition_item_nos IS NULL OR TEI.exhibition_item_no::text = ANY(in_exhibition_item_nos))
    GROUP BY TEI.exhibition_no
) TEI_MAX
  ON TEI_MAX.exhibition_no = TE.exhibition_no
LEFT JOIN t_exhibition_member TEM
  ON TEM.exhibition_no = TE.exhibition_no
  AND TEM.member_no = 10  -- in_member_no
LEFT JOIN t_exhibition_localized TEL
  ON TEL.exhibition_no = TE.exhibition_no
  AND TEL.language_code = 'en'
WHERE TE.tenant_no = 1
  -- Add your specific filters here:
  -- AND (in_exhibition_nos IS NULL OR TE.exhibition_no = ANY(in_exhibition_nos))
  -- AND (in_exhibition_nos IS NOT NULL OR TE.status = 0) -- 入札開始から入札終了(延長含む)商品を表示する = 未確定のみ
  -- AND (in_auction_classification IS NULL OR TE.exhibition_classification_info->>'auctionClassification' = in_auction_classification)
  AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
  AND now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime)
  -- AND (in_exhibition_nos IS NOT NULL OR now() < TEI_MAX.extended_end_datetime)
  -- AND (in_areas IS NULL OR array_length(in_areas, 1) IS NULL OR TE.category_id = ANY(in_areas))
  AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
ORDER BY TE.exhibition_no
LIMIT 20;  -- Limit for debugging

-- 3.3: Check time-based filters
SELECT 'time_check' as query_name,
       now() as current_time,
       'preview_start_datetime' as field,
       MIN(COALESCE(TE.preview_start_datetime, TE.start_datetime)) as min_time,
       MAX(COALESCE(TE.preview_start_datetime, TE.start_datetime)) as max_time,
       COUNT(*) as count
FROM t_exhibition TE
WHERE TE.tenant_no = 1
  AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)

UNION ALL

SELECT 'time_check' as query_name,
       now() as current_time,
       'preview_end_datetime' as field,
       MIN(COALESCE(TE.preview_end_datetime, TE.end_datetime)) as min_time,
       MAX(COALESCE(TE.preview_end_datetime, TE.end_datetime)) as max_time,
       COUNT(*) as count
FROM t_exhibition TE
WHERE TE.tenant_no = 1
  AND (TE.delete_flag IS NULL OR TE.delete_flag = 0);
