-- Debug Query 2: Category Queries
-- This query extracts the category-related CTEs from the original function
-- Run this after the parameter setup to see how categories are processed

-- 2.1: Get categories excluding "other" (category_other = '3')
SELECT 'productCategoryExcludeOther' as query_name, * FROM (
    SELECT mcl.value1
              , mcl.value2
              , unnest(regexp_split_to_array(value3,',')) as value3
    FROM m_constant mc
      INNER JOIN m_constant_localized mcl
          ON mcl.constant_no = mc.constant_no
    WHERE mc.tenant_no = 1
      AND mcl.language_code = 'en'
      AND mc.key_string = 'PRODUCT_CATEGORY'
      AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
      AND mcl.value1 <> '3'  -- category_other
) as productCategoryExcludeOther

UNION ALL

-- 2.2: Get all categories including "other"
SELECT 'productCategoryIncludeOther' as query_name, * FROM (
    SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(value3,',')) as value3
    FROM m_constant mc
      INNER JOIN m_constant_localized mcl
          ON mcl.constant_no = mc.constant_no
    WHERE mc.tenant_no = 1
      AND mcl.language_code = 'en'
      AND mc.key_string = 'PRODUCT_CATEGORY'
      AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
) as productCategoryIncludeOther

ORDER BY query_name, value1;

-- 2.3: Get combined categories from items
SELECT 'combined_categories' as query_name,
       item_no,
       free_field->>'category' AS category_code,
       free_field
FROM t_item_localized
WHERE tenant_no = 1
  AND language_code = 'en'
  AND (delete_flag IS NULL OR delete_flag = 0)
LIMIT 20;  -- Limit for debugging

-- 2.4: Count categories for debugging
SELECT 'category_counts' as info,
       'total_categories_exclude_other' as type,
       COUNT(*) as count
FROM (
    SELECT mcl.value1
    FROM m_constant mc
      INNER JOIN m_constant_localized mcl
          ON mcl.constant_no = mc.constant_no
    WHERE mc.tenant_no = 1
      AND mcl.language_code = 'en'
      AND mc.key_string = 'PRODUCT_CATEGORY'
      AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
      AND mcl.value1 <> '3'
) as exclude_other

UNION ALL

SELECT 'category_counts' as info,
       'total_categories_include_other' as type,
       COUNT(*) as count
FROM (
    SELECT mcl.value1
    FROM m_constant mc
      INNER JOIN m_constant_localized mcl
          ON mcl.constant_no = mc.constant_no
    WHERE mc.tenant_no = 1
      AND mcl.language_code = 'en'
      AND mc.key_string = 'PRODUCT_CATEGORY'
      AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
) as include_other;
